"""
Enhancement Cache System
Redis-based caching for tool enhancement results with configurable TTL,
cache invalidation, and intelligent cache management.
"""

import json
import time
import hashlib
import logging
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, asdict
from enum import Enum
import redis
from config import config

logger = logging.getLogger(__name__)

class CacheLevel(Enum):
    """Cache levels with different TTL values"""
    TEMPORARY = "temporary"  # 1 hour
    SHORT_TERM = "short_term"  # 24 hours
    MEDIUM_TERM = "medium_term"  # 7 days
    LONG_TERM = "long_term"  # 30 days
    PERMANENT = "permanent"  # No expiration

@dataclass
class CacheEntry:
    """Cache entry with metadata"""
    data: Dict[str, Any]
    timestamp: float
    ttl: int
    cache_level: str
    version: str
    metadata: Dict[str, Any]

class EnhancementCache:
    """
    Redis-based caching system for enhancement results
    """
    
    def __init__(self, redis_host: str = "localhost", redis_port: int = 6379, 
                 redis_db: int = 0, redis_password: Optional[str] = None):
        """
        Initialize the cache system
        
        Args:
            redis_host: Redis server host
            redis_port: Redis server port
            redis_db: Redis database number
            redis_password: Redis password (if required)
        """
        self.logger = logging.getLogger(__name__)
        
        # TTL values for different cache levels (in seconds)
        self.ttl_values = {
            CacheLevel.TEMPORARY: 3600,      # 1 hour
            CacheLevel.SHORT_TERM: 86400,    # 24 hours
            CacheLevel.MEDIUM_TERM: 604800,  # 7 days
            CacheLevel.LONG_TERM: 2592000,   # 30 days
            CacheLevel.PERMANENT: -1         # No expiration
        }
        
        # Cache key prefixes
        self.key_prefixes = {
            'enhancement': 'enh:',
            'website_data': 'web:',
            'ai_response': 'ai:',
            'performance': 'perf:',
            'metadata': 'meta:'
        }
        
        # Initialize Redis connection
        try:
            self.redis_client = redis.Redis(
                host=redis_host,
                port=redis_port,
                db=redis_db,
                password=redis_password,
                decode_responses=True,
                socket_timeout=5,
                socket_connect_timeout=5
            )
            
            # Test connection
            self.redis_client.ping()
            self.logger.info(f"Connected to Redis at {redis_host}:{redis_port}")
            
        except Exception as e:
            self.logger.error(f"Failed to connect to Redis: {str(e)}")
            # Fallback to in-memory cache
            self.redis_client = None
            self._memory_cache = {}
            self.logger.warning("Using in-memory cache as fallback")

    def cache_enhancement_result(self, tool_id: str, data: Dict[str, Any], 
                               cache_level: CacheLevel = CacheLevel.SHORT_TERM,
                               metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        Cache enhancement result for a tool
        
        Args:
            tool_id: Unique identifier for the tool
            data: Enhancement data to cache
            cache_level: Cache level determining TTL
            metadata: Additional metadata about the cache entry
            
        Returns:
            bool: True if cached successfully, False otherwise
        """
        try:
            cache_key = self._generate_cache_key('enhancement', tool_id)
            ttl = self.ttl_values[cache_level]
            
            cache_entry = CacheEntry(
                data=data,
                timestamp=time.time(),
                ttl=ttl,
                cache_level=cache_level.value,
                version="1.0",
                metadata=metadata or {}
            )
            
            serialized_data = json.dumps(asdict(cache_entry), default=str)
            
            if self.redis_client:
                if ttl > 0:
                    self.redis_client.setex(cache_key, ttl, serialized_data)
                else:
                    self.redis_client.set(cache_key, serialized_data)
            else:
                # Fallback to memory cache
                self._memory_cache[cache_key] = {
                    'data': serialized_data,
                    'expires': time.time() + ttl if ttl > 0 else float('inf')
                }
            
            self.logger.debug(f"Cached enhancement result for {tool_id} with TTL {ttl}s")
            return True
            
        except Exception as e:
            self.logger.error(f"Error caching enhancement result for {tool_id}: {str(e)}")
            return False

    def get_cached_enhancement(self, tool_id: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve cached enhancement result
        
        Args:
            tool_id: Unique identifier for the tool
            
        Returns:
            Dict containing cached data or None if not found/expired
        """
        try:
            cache_key = self._generate_cache_key('enhancement', tool_id)
            
            if self.redis_client:
                cached_data = self.redis_client.get(cache_key)
            else:
                # Check memory cache
                cache_entry = self._memory_cache.get(cache_key)
                if cache_entry and time.time() < cache_entry['expires']:
                    cached_data = cache_entry['data']
                else:
                    cached_data = None
                    if cache_entry:  # Expired
                        del self._memory_cache[cache_key]
            
            if cached_data:
                cache_entry = json.loads(cached_data)
                self.logger.debug(f"Cache hit for {tool_id}")
                return cache_entry['data']
            else:
                self.logger.debug(f"Cache miss for {tool_id}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error retrieving cached data for {tool_id}: {str(e)}")
            return None

    def cache_website_data(self, url: str, data: Dict[str, Any], 
                          cache_level: CacheLevel = CacheLevel.MEDIUM_TERM) -> bool:
        """
        Cache website scraping data
        
        Args:
            url: Website URL
            data: Scraped website data
            cache_level: Cache level determining TTL
            
        Returns:
            bool: True if cached successfully
        """
        try:
            url_hash = self._hash_url(url)
            cache_key = self._generate_cache_key('website_data', url_hash)
            ttl = self.ttl_values[cache_level]
            
            cache_entry = CacheEntry(
                data=data,
                timestamp=time.time(),
                ttl=ttl,
                cache_level=cache_level.value,
                version="1.0",
                metadata={'url': url}
            )
            
            serialized_data = json.dumps(asdict(cache_entry), default=str)
            
            if self.redis_client:
                if ttl > 0:
                    self.redis_client.setex(cache_key, ttl, serialized_data)
                else:
                    self.redis_client.set(cache_key, serialized_data)
            else:
                self._memory_cache[cache_key] = {
                    'data': serialized_data,
                    'expires': time.time() + ttl if ttl > 0 else float('inf')
                }
            
            self.logger.debug(f"Cached website data for {url}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error caching website data for {url}: {str(e)}")
            return False

    def get_cached_website_data(self, url: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve cached website data
        
        Args:
            url: Website URL
            
        Returns:
            Dict containing cached website data or None
        """
        try:
            url_hash = self._hash_url(url)
            cache_key = self._generate_cache_key('website_data', url_hash)
            
            if self.redis_client:
                cached_data = self.redis_client.get(cache_key)
            else:
                cache_entry = self._memory_cache.get(cache_key)
                if cache_entry and time.time() < cache_entry['expires']:
                    cached_data = cache_entry['data']
                else:
                    cached_data = None
                    if cache_entry:
                        del self._memory_cache[cache_key]
            
            if cached_data:
                cache_entry = json.loads(cached_data)
                return cache_entry['data']
            else:
                return None
                
        except Exception as e:
            self.logger.error(f"Error retrieving cached website data for {url}: {str(e)}")
            return None

    def cache_ai_response(self, prompt_hash: str, response: Dict[str, Any],
                         cache_level: CacheLevel = CacheLevel.LONG_TERM) -> bool:
        """
        Cache AI API response
        
        Args:
            prompt_hash: Hash of the prompt used
            response: AI response data
            cache_level: Cache level determining TTL
            
        Returns:
            bool: True if cached successfully
        """
        try:
            cache_key = self._generate_cache_key('ai_response', prompt_hash)
            ttl = self.ttl_values[cache_level]
            
            cache_entry = CacheEntry(
                data=response,
                timestamp=time.time(),
                ttl=ttl,
                cache_level=cache_level.value,
                version="1.0",
                metadata={'prompt_hash': prompt_hash}
            )
            
            serialized_data = json.dumps(asdict(cache_entry), default=str)
            
            if self.redis_client:
                if ttl > 0:
                    self.redis_client.setex(cache_key, ttl, serialized_data)
                else:
                    self.redis_client.set(cache_key, serialized_data)
            else:
                self._memory_cache[cache_key] = {
                    'data': serialized_data,
                    'expires': time.time() + ttl if ttl > 0 else float('inf')
                }
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error caching AI response: {str(e)}")
            return False

    def get_cached_ai_response(self, prompt_hash: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve cached AI response
        
        Args:
            prompt_hash: Hash of the prompt
            
        Returns:
            Dict containing cached AI response or None
        """
        try:
            cache_key = self._generate_cache_key('ai_response', prompt_hash)
            
            if self.redis_client:
                cached_data = self.redis_client.get(cache_key)
            else:
                cache_entry = self._memory_cache.get(cache_key)
                if cache_entry and time.time() < cache_entry['expires']:
                    cached_data = cache_entry['data']
                else:
                    cached_data = None
                    if cache_entry:
                        del self._memory_cache[cache_key]
            
            if cached_data:
                cache_entry = json.loads(cached_data)
                return cache_entry['data']
            else:
                return None
                
        except Exception as e:
            self.logger.error(f"Error retrieving cached AI response: {str(e)}")
            return None

    def invalidate_cache(self, pattern: str) -> int:
        """
        Invalidate cache entries matching a pattern
        
        Args:
            pattern: Redis pattern to match keys
            
        Returns:
            int: Number of keys deleted
        """
        try:
            if self.redis_client:
                keys = self.redis_client.keys(pattern)
                if keys:
                    deleted = self.redis_client.delete(*keys)
                    self.logger.info(f"Invalidated {deleted} cache entries matching pattern: {pattern}")
                    return deleted
                return 0
            else:
                # Memory cache invalidation
                keys_to_delete = [key for key in self._memory_cache.keys() if self._matches_pattern(key, pattern)]
                for key in keys_to_delete:
                    del self._memory_cache[key]
                return len(keys_to_delete)
                
        except Exception as e:
            self.logger.error(f"Error invalidating cache with pattern {pattern}: {str(e)}")
            return 0

    def get_cache_stats(self) -> Dict[str, Any]:
        """
        Get cache statistics
        
        Returns:
            Dict containing cache statistics
        """
        try:
            if self.redis_client:
                info = self.redis_client.info()
                stats = {
                    'redis_connected': True,
                    'used_memory': info.get('used_memory_human', 'Unknown'),
                    'connected_clients': info.get('connected_clients', 0),
                    'total_commands_processed': info.get('total_commands_processed', 0),
                    'keyspace_hits': info.get('keyspace_hits', 0),
                    'keyspace_misses': info.get('keyspace_misses', 0)
                }
                
                # Calculate hit rate
                hits = stats['keyspace_hits']
                misses = stats['keyspace_misses']
                if hits + misses > 0:
                    stats['hit_rate'] = round((hits / (hits + misses)) * 100, 2)
                else:
                    stats['hit_rate'] = 0.0
                    
            else:
                stats = {
                    'redis_connected': False,
                    'memory_cache_size': len(self._memory_cache),
                    'fallback_mode': True
                }
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Error getting cache stats: {str(e)}")
            return {'error': str(e)}

    def cleanup_expired_entries(self) -> int:
        """
        Clean up expired entries (mainly for memory cache)
        
        Returns:
            int: Number of entries cleaned up
        """
        if self.redis_client:
            # Redis handles expiration automatically
            return 0
        
        # Clean up memory cache
        current_time = time.time()
        expired_keys = [
            key for key, entry in self._memory_cache.items()
            if entry['expires'] != float('inf') and current_time >= entry['expires']
        ]
        
        for key in expired_keys:
            del self._memory_cache[key]
        
        if expired_keys:
            self.logger.info(f"Cleaned up {len(expired_keys)} expired cache entries")
        
        return len(expired_keys)

    def _generate_cache_key(self, prefix_type: str, identifier: str) -> str:
        """Generate cache key with prefix"""
        prefix = self.key_prefixes.get(prefix_type, 'misc:')
        return f"{prefix}{identifier}"

    def _hash_url(self, url: str) -> str:
        """Generate hash for URL"""
        return hashlib.md5(url.encode('utf-8')).hexdigest()

    def _hash_prompt(self, prompt: str) -> str:
        """Generate hash for AI prompt"""
        return hashlib.sha256(prompt.encode('utf-8')).hexdigest()

    def _matches_pattern(self, key: str, pattern: str) -> bool:
        """Check if key matches pattern (simple implementation)"""
        if '*' in pattern:
            pattern_parts = pattern.split('*')
            if len(pattern_parts) == 2:
                return key.startswith(pattern_parts[0]) and key.endswith(pattern_parts[1])
        return key == pattern

    def batch_cache_enhancement_results(self, results: List[Dict[str, Any]], 
                                      cache_level: CacheLevel = CacheLevel.SHORT_TERM) -> int:
        """
        Cache multiple enhancement results in batch
        
        Args:
            results: List of enhancement results with 'tool_id' and 'data' keys
            cache_level: Cache level for all entries
            
        Returns:
            int: Number of successfully cached entries
        """
        cached_count = 0
        
        for result in results:
            tool_id = result.get('tool_id')
            data = result.get('data')
            
            if tool_id and data:
                if self.cache_enhancement_result(tool_id, data, cache_level):
                    cached_count += 1
        
        self.logger.info(f"Batch cached {cached_count}/{len(results)} enhancement results")
        return cached_count

    def get_cache_key_info(self, key: str) -> Optional[Dict[str, Any]]:
        """
        Get information about a specific cache key
        
        Args:
            key: Cache key to inspect
            
        Returns:
            Dict with key information or None
        """
        try:
            if self.redis_client:
                if self.redis_client.exists(key):
                    ttl = self.redis_client.ttl(key)
                    data_type = self.redis_client.type(key)
                    return {
                        'exists': True,
                        'ttl': ttl,
                        'type': data_type,
                        'size': len(str(self.redis_client.get(key) or ''))
                    }
                else:
                    return {'exists': False}
            else:
                if key in self._memory_cache:
                    entry = self._memory_cache[key]
                    remaining_ttl = entry['expires'] - time.time() if entry['expires'] != float('inf') else -1
                    return {
                        'exists': True,
                        'ttl': int(remaining_ttl) if remaining_ttl > 0 else -1,
                        'type': 'string',
                        'size': len(entry['data'])
                    }
                else:
                    return {'exists': False}
                    
        except Exception as e:
            self.logger.error(f"Error getting cache key info for {key}: {str(e)}")
            return None

# Global cache instance
cache = EnhancementCache(
    redis_host=getattr(config, 'redis_host', 'localhost'),
    redis_port=getattr(config, 'redis_port', 6379),
    redis_db=getattr(config, 'redis_db', 0),
    redis_password=getattr(config, 'redis_password', None)
)
