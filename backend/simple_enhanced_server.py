"""
Simple Enhanced Backend Server using Flask
Provides basic API endpoints for Phase 3 features without requiring FastAPI
"""

import os
import sys
import json
import time
import threading
from flask import Flask, request, jsonify
from flask_cors import CORS

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Try to import Phase 3 components
try:
    from enhanced_scraper_pipeline_phase3 import EnhancedScraperPipelinePhase3
    ENHANCED_PIPELINE_AVAILABLE = True
except ImportError as e:
    print(f"⚠️  Enhanced pipeline not available: {e}")
    ENHANCED_PIPELINE_AVAILABLE = False

# Fallback imports
try:
    from scraper_pipeline import Scraper<PERSON>ipeline
    TRADITIONAL_PIPELINE_AVAILABLE = True
except ImportError:
    TRADITIONAL_PIPELINE_AVAILABLE = False

# Initialize Flask app
app = Flask(__name__)
CORS(app)

# Global variables
traditional_pipeline = None
enhanced_pipeline = None
active_jobs = {}

def initialize_pipelines():
    """Initialize both traditional and enhanced pipelines"""
    global traditional_pipeline, enhanced_pipeline
    
    try:
        # Initialize traditional pipeline
        if TRADITIONAL_PIPELINE_AVAILABLE:
            traditional_pipeline = ScraperPipeline()
            print("✅ Traditional scraper pipeline initialized")
        
        # Initialize enhanced pipeline
        if ENHANCED_PIPELINE_AVAILABLE:
            api_key = '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'
            enhanced_pipeline = EnhancedScraperPipelinePhase3(api_key)
            print("✅ Enhanced Phase 3 pipeline initialized")
        else:
            print("⚠️  Enhanced Phase 3 pipeline not available")
            
    except Exception as e:
        print(f"❌ Failed to initialize pipelines: {str(e)}")

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        "status": "healthy",
        "timestamp": time.time(),
        "traditional_pipeline": traditional_pipeline is not None,
        "enhanced_pipeline": enhanced_pipeline is not None,
        "phase3_available": ENHANCED_PIPELINE_AVAILABLE
    })

@app.route('/api/capabilities', methods=['GET'])
def get_capabilities():
    """Get available pipeline capabilities"""
    return jsonify({
        "traditional_scraping": traditional_pipeline is not None,
        "enhanced_scraping": enhanced_pipeline is not None,
        "phase3_features": {
            "structured_data_extraction": ENHANCED_PIPELINE_AVAILABLE,
            "advanced_content_analysis": ENHANCED_PIPELINE_AVAILABLE,
            "performance_analysis": ENHANCED_PIPELINE_AVAILABLE,
            "parallel_processing": ENHANCED_PIPELINE_AVAILABLE,
            "caching_system": ENHANCED_PIPELINE_AVAILABLE,
            "performance_monitoring": ENHANCED_PIPELINE_AVAILABLE
        }
    })

@app.route('/api/spiders', methods=['GET'])
def get_available_spiders():
    """Get list of available Scrapy spiders"""
    try:
        if traditional_pipeline:
            spiders = traditional_pipeline.get_available_spiders()
        else:
            spiders = ["ai_tools_spider", "product_hunt_spider"]  # Default list
        return jsonify({"spiders": spiders})
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/start-enhanced-scraping', methods=['POST'])
def start_enhanced_scraping_job():
    """Start an enhanced scraping job with Phase 3 features"""
    try:
        if not enhanced_pipeline:
            return jsonify({
                "success": False,
                "error": "Enhanced pipeline not available. Phase 3 features require proper configuration."
            }), 503
        
        data = request.get_json()
        tools = data.get('tools', [])
        use_parallel = data.get('use_parallel', True)
        use_phase3 = data.get('use_phase3', True)
        
        if not tools:
            return jsonify({"success": False, "error": "No tools provided"}), 400
        
        # Validate tool data
        for tool in tools:
            if not tool.get('name') or not tool.get('url'):
                return jsonify({
                    "success": False, 
                    "error": "Each tool must have 'name' and 'url'"
                }), 400
        
        # Generate job ID
        job_id = f"enhanced_{int(time.time())}"
        
        # Estimate processing time
        estimated_time = len(tools) * (2.0 if use_parallel else 5.0)
        
        # Track job
        active_jobs[job_id] = {
            "status": "running",
            "start_time": time.time(),
            "total_tools": len(tools),
            "phase3_enabled": use_phase3,
            "parallel_enabled": use_parallel,
            "progress": 0,
            "results": None
        }
        
        # Start enhanced processing in background thread
        def run_enhanced_scraper():
            try:
                print(f"🚀 Starting enhanced scraping job {job_id} with {len(tools)} tools")
                
                result = enhanced_pipeline.process_tools_enhanced(
                    tools,
                    use_parallel=use_parallel
                )
                
                # Update job status
                active_jobs[job_id]["status"] = "completed"
                active_jobs[job_id]["results"] = result
                active_jobs[job_id]["end_time"] = time.time()
                active_jobs[job_id]["progress"] = 100
                
                print(f"✅ Enhanced scraping job {job_id} completed successfully")
                
            except Exception as e:
                print(f"❌ Enhanced scraping job {job_id} failed: {str(e)}")
                active_jobs[job_id]["status"] = "failed"
                active_jobs[job_id]["error"] = str(e)
                active_jobs[job_id]["end_time"] = time.time()
        
        # Start background thread
        thread = threading.Thread(target=run_enhanced_scraper)
        thread.daemon = True
        thread.start()
        
        return jsonify({
            "success": True,
            "job_id": job_id,
            "message": f"Started enhanced scraping job for {len(tools)} tools",
            "total_tools": len(tools),
            "processing_mode": "parallel" if use_parallel else "sequential",
            "phase3_enabled": use_phase3,
            "estimated_time": estimated_time
        })
        
    except Exception as e:
        print(f"❌ Error starting enhanced scraping job: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/job-status/<job_id>', methods=['GET'])
def get_job_status(job_id):
    """Get status of an enhanced scraping job"""
    if job_id not in active_jobs:
        return jsonify({"error": "Job not found"}), 404
    
    job = active_jobs[job_id]
    
    # Calculate progress
    progress = 0
    if job["status"] == "completed":
        progress = 100
    elif job["status"] == "running":
        elapsed_time = time.time() - job["start_time"]
        estimated_total = job.get("estimated_time", 60)
        progress = min(90, (elapsed_time / estimated_total) * 100)
    
    response = {
        "job_id": job_id,
        "status": job["status"],
        "progress": progress,
        "total_tools": job["total_tools"],
        "phase3_enabled": job["phase3_enabled"],
        "parallel_enabled": job["parallel_enabled"]
    }
    
    if job["status"] == "completed" and job.get("results"):
        response["results"] = job["results"]
    elif job["status"] == "failed":
        response["error"] = job.get("error", "Unknown error")
    
    return jsonify(response)

@app.route('/api/enhanced-results/<job_id>', methods=['GET'])
def get_enhanced_results(job_id):
    """Get detailed results from an enhanced scraping job"""
    if job_id not in active_jobs:
        return jsonify({"error": "Job not found"}), 404
    
    job = active_jobs[job_id]
    
    if job["status"] != "completed":
        return jsonify({"error": f"Job status: {job['status']}"}), 400
    
    if not job.get("results"):
        return jsonify({"error": "No results available"}), 404
    
    return jsonify(job["results"])

@app.route('/api/performance-dashboard', methods=['GET'])
def get_performance_dashboard():
    """Get performance dashboard data from enhanced pipeline"""
    try:
        if not enhanced_pipeline:
            return jsonify({"error": "Enhanced pipeline not available"}), 503
        
        stats = enhanced_pipeline.get_comprehensive_stats()
        return jsonify(stats)
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/test-services', methods=['GET'])
def test_services():
    """Test all pipeline services"""
    try:
        results = {}
        
        # Test traditional pipeline
        if traditional_pipeline:
            results["traditional_pipeline"] = {"status": "available"}
        
        # Test enhanced pipeline components
        if enhanced_pipeline:
            results["enhanced_pipeline"] = {
                "status": "available",
                "components": {
                    "structured_data_extractor": "available",
                    "content_analyzer": "available", 
                    "performance_analyzer": "available",
                    "parallel_processor": "available",
                    "performance_monitor": "available"
                }
            }
        
        return jsonify(results)
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/jobs', methods=['GET'])
def list_jobs():
    """List all jobs"""
    return jsonify({"jobs": active_jobs})

if __name__ == "__main__":
    print("🚀 Starting Simple Enhanced Backend Server...")
    print("=" * 50)
    
    # Initialize pipelines
    initialize_pipelines()
    
    print(f"\n📊 Server Status:")
    print(f"   Traditional Pipeline: {'✅' if traditional_pipeline else '❌'}")
    print(f"   Enhanced Pipeline: {'✅' if enhanced_pipeline else '❌'}")
    print(f"   Phase 3 Available: {'✅' if ENHANCED_PIPELINE_AVAILABLE else '❌'}")
    
    print(f"\n🌐 Starting server on http://localhost:8001")
    print(f"   Press Ctrl+C to stop")
    
    # Run Flask app
    app.run(host='0.0.0.0', port=8001, debug=False, threaded=True)
