"""
Parallel Processing System
Implements batch processing for multiple tools, parallel API calls,
and optimized processing pipeline for maximum speed and efficiency.
"""

import asyncio
import concurrent.futures
import threading
import time
import logging
from typing import Dict, List, Any, Optional, Callable, Tuple, Union
from dataclasses import dataclass
from enum import Enum
import queue
from functools import partial
import multiprocessing as mp

logger = logging.getLogger(__name__)

class ProcessingMode(Enum):
    SEQUENTIAL = "sequential"
    THREADED = "threaded"
    ASYNC = "async"
    MULTIPROCESS = "multiprocess"
    HYBRID = "hybrid"

@dataclass
class ProcessingTask:
    """Individual processing task"""
    task_id: str
    tool_name: str
    url: str
    data: Dict[str, Any]
    priority: int = 1  # 1 = highest, 5 = lowest
    retry_count: int = 0
    max_retries: int = 3

@dataclass
class ProcessingResult:
    """Result of processing task"""
    task_id: str
    success: bool
    data: Optional[Dict[str, Any]]
    error: Optional[str]
    processing_time: float
    retry_count: int

class ParallelProcessingSystem:
    """
    Advanced parallel processing system for tool enhancement
    """
    
    def __init__(self, max_workers: int = None, max_concurrent_api_calls: int = 10):
        """
        Initialize parallel processing system
        
        Args:
            max_workers: Maximum number of worker threads/processes
            max_concurrent_api_calls: Maximum concurrent API calls
        """
        self.logger = logging.getLogger(__name__)
        
        # Determine optimal worker count
        if max_workers is None:
            self.max_workers = min(32, (mp.cpu_count() or 1) + 4)
        else:
            self.max_workers = max_workers
        
        self.max_concurrent_api_calls = max_concurrent_api_calls
        
        # Processing queues
        self.high_priority_queue = queue.PriorityQueue()
        self.normal_priority_queue = queue.PriorityQueue()
        self.low_priority_queue = queue.PriorityQueue()
        
        # Results storage
        self.results = {}
        self.results_lock = threading.Lock()
        
        # Performance tracking
        self.stats = {
            'total_processed': 0,
            'successful_processed': 0,
            'failed_processed': 0,
            'total_processing_time': 0.0,
            'average_processing_time': 0.0,
            'concurrent_tasks': 0,
            'peak_concurrent_tasks': 0
        }
        self.stats_lock = threading.Lock()
        
        # API rate limiting
        self.api_semaphore = asyncio.Semaphore(max_concurrent_api_calls)
        self.api_call_times = []
        self.api_rate_limit_lock = threading.Lock()
        
        self.logger.info(f"Initialized parallel processing system with {self.max_workers} workers")

    def process_batch(self, tasks: List[ProcessingTask], 
                     processing_function: Callable,
                     mode: ProcessingMode = ProcessingMode.HYBRID) -> List[ProcessingResult]:
        """
        Process a batch of tasks using specified mode
        
        Args:
            tasks: List of processing tasks
            processing_function: Function to process each task
            mode: Processing mode to use
            
        Returns:
            List of processing results
        """
        start_time = time.time()
        self.logger.info(f"Starting batch processing of {len(tasks)} tasks using {mode.value} mode")
        
        # Sort tasks by priority
        sorted_tasks = sorted(tasks, key=lambda t: t.priority)
        
        if mode == ProcessingMode.SEQUENTIAL:
            results = self._process_sequential(sorted_tasks, processing_function)
        elif mode == ProcessingMode.THREADED:
            results = self._process_threaded(sorted_tasks, processing_function)
        elif mode == ProcessingMode.ASYNC:
            results = self._process_async(sorted_tasks, processing_function)
        elif mode == ProcessingMode.MULTIPROCESS:
            results = self._process_multiprocess(sorted_tasks, processing_function)
        elif mode == ProcessingMode.HYBRID:
            results = self._process_hybrid(sorted_tasks, processing_function)
        else:
            raise ValueError(f"Unsupported processing mode: {mode}")
        
        total_time = time.time() - start_time
        self._update_batch_stats(results, total_time)
        
        self.logger.info(f"Completed batch processing in {total_time:.2f}s")
        return results

    def _process_sequential(self, tasks: List[ProcessingTask], 
                          processing_function: Callable) -> List[ProcessingResult]:
        """Process tasks sequentially"""
        results = []
        
        for task in tasks:
            start_time = time.time()
            try:
                result_data = processing_function(task)
                processing_time = time.time() - start_time
                
                result = ProcessingResult(
                    task_id=task.task_id,
                    success=True,
                    data=result_data,
                    error=None,
                    processing_time=processing_time,
                    retry_count=task.retry_count
                )
                
            except Exception as e:
                processing_time = time.time() - start_time
                result = ProcessingResult(
                    task_id=task.task_id,
                    success=False,
                    data=None,
                    error=str(e),
                    processing_time=processing_time,
                    retry_count=task.retry_count
                )
            
            results.append(result)
            self._update_concurrent_stats(1, -1)  # Track completion
        
        return results

    def _process_threaded(self, tasks: List[ProcessingTask], 
                         processing_function: Callable) -> List[ProcessingResult]:
        """Process tasks using thread pool"""
        results = []
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all tasks
            future_to_task = {
                executor.submit(self._execute_task, task, processing_function): task
                for task in tasks
            }
            
            # Collect results as they complete
            for future in concurrent.futures.as_completed(future_to_task):
                task = future_to_task[future]
                try:
                    result = future.result()
                    results.append(result)
                except Exception as e:
                    result = ProcessingResult(
                        task_id=task.task_id,
                        success=False,
                        data=None,
                        error=str(e),
                        processing_time=0.0,
                        retry_count=task.retry_count
                    )
                    results.append(result)
        
        return results

    def _process_async(self, tasks: List[ProcessingTask], 
                      processing_function: Callable) -> List[ProcessingResult]:
        """Process tasks using async/await"""
        async def process_async_batch():
            semaphore = asyncio.Semaphore(self.max_workers)
            
            async def process_single_task(task):
                async with semaphore:
                    return await self._execute_task_async(task, processing_function)
            
            # Create coroutines for all tasks
            coroutines = [process_single_task(task) for task in tasks]
            
            # Execute all tasks concurrently
            results = await asyncio.gather(*coroutines, return_exceptions=True)
            
            # Convert exceptions to error results
            processed_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    processed_results.append(ProcessingResult(
                        task_id=tasks[i].task_id,
                        success=False,
                        data=None,
                        error=str(result),
                        processing_time=0.0,
                        retry_count=tasks[i].retry_count
                    ))
                else:
                    processed_results.append(result)
            
            return processed_results
        
        # Run the async batch
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        
        return loop.run_until_complete(process_async_batch())

    def _process_multiprocess(self, tasks: List[ProcessingTask], 
                            processing_function: Callable) -> List[ProcessingResult]:
        """Process tasks using multiprocessing"""
        results = []
        
        # Create a wrapper function for multiprocessing
        def mp_wrapper(task_data):
            task = ProcessingTask(**task_data)
            return self._execute_task(task, processing_function)
        
        # Convert tasks to serializable format
        task_data_list = [
            {
                'task_id': task.task_id,
                'tool_name': task.tool_name,
                'url': task.url,
                'data': task.data,
                'priority': task.priority,
                'retry_count': task.retry_count,
                'max_retries': task.max_retries
            }
            for task in tasks
        ]
        
        with concurrent.futures.ProcessPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_task = {
                executor.submit(mp_wrapper, task_data): task_data
                for task_data in task_data_list
            }
            
            for future in concurrent.futures.as_completed(future_to_task):
                task_data = future_to_task[future]
                try:
                    result = future.result()
                    results.append(result)
                except Exception as e:
                    result = ProcessingResult(
                        task_id=task_data['task_id'],
                        success=False,
                        data=None,
                        error=str(e),
                        processing_time=0.0,
                        retry_count=task_data['retry_count']
                    )
                    results.append(result)
        
        return results

    def _process_hybrid(self, tasks: List[ProcessingTask], 
                       processing_function: Callable) -> List[ProcessingResult]:
        """Process tasks using hybrid approach (async + threading)"""
        # Separate tasks by type/complexity
        io_intensive_tasks = []
        cpu_intensive_tasks = []
        
        for task in tasks:
            # Simple heuristic: tasks with URLs are likely I/O intensive
            if task.url:
                io_intensive_tasks.append(task)
            else:
                cpu_intensive_tasks.append(task)
        
        results = []
        
        # Process I/O intensive tasks with async
        if io_intensive_tasks:
            async_results = self._process_async(io_intensive_tasks, processing_function)
            results.extend(async_results)
        
        # Process CPU intensive tasks with threading
        if cpu_intensive_tasks:
            threaded_results = self._process_threaded(cpu_intensive_tasks, processing_function)
            results.extend(threaded_results)
        
        return results

    def _execute_task(self, task: ProcessingTask, 
                     processing_function: Callable) -> ProcessingResult:
        """Execute a single task with error handling and retries"""
        start_time = time.time()
        self._update_concurrent_stats(1, 0)  # Track start
        
        try:
            result_data = processing_function(task)
            processing_time = time.time() - start_time
            
            result = ProcessingResult(
                task_id=task.task_id,
                success=True,
                data=result_data,
                error=None,
                processing_time=processing_time,
                retry_count=task.retry_count
            )
            
        except Exception as e:
            processing_time = time.time() - start_time
            
            # Check if we should retry
            if task.retry_count < task.max_retries:
                self.logger.warning(f"Task {task.task_id} failed, retrying ({task.retry_count + 1}/{task.max_retries}): {str(e)}")
                task.retry_count += 1
                time.sleep(2 ** task.retry_count)  # Exponential backoff
                return self._execute_task(task, processing_function)
            
            result = ProcessingResult(
                task_id=task.task_id,
                success=False,
                data=None,
                error=str(e),
                processing_time=processing_time,
                retry_count=task.retry_count
            )
        
        finally:
            self._update_concurrent_stats(-1, 0)  # Track completion
        
        return result

    async def _execute_task_async(self, task: ProcessingTask, 
                                 processing_function: Callable) -> ProcessingResult:
        """Execute a single task asynchronously"""
        start_time = time.time()
        self._update_concurrent_stats(1, 0)
        
        try:
            # If processing function is not async, run it in thread pool
            if not asyncio.iscoroutinefunction(processing_function):
                loop = asyncio.get_event_loop()
                result_data = await loop.run_in_executor(None, processing_function, task)
            else:
                result_data = await processing_function(task)
            
            processing_time = time.time() - start_time
            
            result = ProcessingResult(
                task_id=task.task_id,
                success=True,
                data=result_data,
                error=None,
                processing_time=processing_time,
                retry_count=task.retry_count
            )
            
        except Exception as e:
            processing_time = time.time() - start_time
            
            if task.retry_count < task.max_retries:
                task.retry_count += 1
                await asyncio.sleep(2 ** task.retry_count)
                return await self._execute_task_async(task, processing_function)
            
            result = ProcessingResult(
                task_id=task.task_id,
                success=False,
                data=None,
                error=str(e),
                processing_time=processing_time,
                retry_count=task.retry_count
            )
        
        finally:
            self._update_concurrent_stats(-1, 0)
        
        return result

    def _update_concurrent_stats(self, concurrent_change: int, completion_change: int):
        """Update concurrent processing statistics"""
        with self.stats_lock:
            self.stats['concurrent_tasks'] += concurrent_change
            if self.stats['concurrent_tasks'] > self.stats['peak_concurrent_tasks']:
                self.stats['peak_concurrent_tasks'] = self.stats['concurrent_tasks']

    def _update_batch_stats(self, results: List[ProcessingResult], total_time: float):
        """Update batch processing statistics"""
        with self.stats_lock:
            successful = sum(1 for r in results if r.success)
            failed = len(results) - successful
            total_processing_time = sum(r.processing_time for r in results)
            
            self.stats['total_processed'] += len(results)
            self.stats['successful_processed'] += successful
            self.stats['failed_processed'] += failed
            self.stats['total_processing_time'] += total_processing_time
            
            if self.stats['total_processed'] > 0:
                self.stats['average_processing_time'] = (
                    self.stats['total_processing_time'] / self.stats['total_processed']
                )

    async def rate_limited_api_call(self, api_function: Callable, *args, **kwargs) -> Any:
        """
        Make rate-limited API call
        
        Args:
            api_function: API function to call
            *args, **kwargs: Arguments for the API function
            
        Returns:
            API response
        """
        async with self.api_semaphore:
            # Track API call timing for rate limiting
            current_time = time.time()
            
            with self.api_rate_limit_lock:
                # Remove old call times (older than 1 minute)
                self.api_call_times = [
                    t for t in self.api_call_times 
                    if current_time - t < 60
                ]
                
                # Check if we need to wait
                if len(self.api_call_times) >= self.max_concurrent_api_calls:
                    wait_time = 60 - (current_time - self.api_call_times[0])
                    if wait_time > 0:
                        await asyncio.sleep(wait_time)
                
                self.api_call_times.append(current_time)
            
            # Make the API call
            if asyncio.iscoroutinefunction(api_function):
                return await api_function(*args, **kwargs)
            else:
                loop = asyncio.get_event_loop()
                return await loop.run_in_executor(None, api_function, *args, **kwargs)

    def get_processing_stats(self) -> Dict[str, Any]:
        """Get current processing statistics"""
        with self.stats_lock:
            stats = self.stats.copy()
        
        # Calculate additional metrics
        if stats['total_processed'] > 0:
            stats['success_rate'] = (stats['successful_processed'] / stats['total_processed']) * 100
            stats['failure_rate'] = (stats['failed_processed'] / stats['total_processed']) * 100
        else:
            stats['success_rate'] = 0.0
            stats['failure_rate'] = 0.0
        
        stats['current_concurrent_tasks'] = stats['concurrent_tasks']
        
        return stats

    def reset_stats(self):
        """Reset processing statistics"""
        with self.stats_lock:
            self.stats = {
                'total_processed': 0,
                'successful_processed': 0,
                'failed_processed': 0,
                'total_processing_time': 0.0,
                'average_processing_time': 0.0,
                'concurrent_tasks': 0,
                'peak_concurrent_tasks': 0
            }

    def optimize_batch_size(self, total_tasks: int, target_time: float = 300.0) -> int:
        """
        Optimize batch size based on performance history
        
        Args:
            total_tasks: Total number of tasks to process
            target_time: Target processing time in seconds
            
        Returns:
            Optimal batch size
        """
        if self.stats['average_processing_time'] > 0:
            # Estimate optimal batch size based on target time
            estimated_batch_size = int(target_time / self.stats['average_processing_time'])
            
            # Apply constraints
            min_batch_size = max(1, self.max_workers)
            max_batch_size = min(total_tasks, self.max_workers * 4)
            
            optimal_batch_size = max(min_batch_size, min(estimated_batch_size, max_batch_size))
        else:
            # Default to worker count if no history
            optimal_batch_size = min(total_tasks, self.max_workers)
        
        self.logger.info(f"Optimized batch size: {optimal_batch_size} for {total_tasks} tasks")
        return optimal_batch_size

# Global parallel processing instance
parallel_processor = ParallelProcessingSystem()
