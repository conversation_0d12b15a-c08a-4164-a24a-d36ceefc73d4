"""
Enhanced Scraper Pipeline with Phase 3 Advanced Features
Integrates all Phase 3 components into the existing scraping and enhancement workflow.
"""

import logging
import time
import json
from typing import Dict, List, Any, Optional
from dataclasses import asdict

# Import existing components
from ai_navigator_client import AINavigatorClient
from data_enrichment_service import DataEnrichmentService
from enhanced_taxonomy_service import EnhancedTaxonomyService

# Import Phase 3 components
from structured_data_extractor import StructuredDataExtractor
from advanced_content_analyzer import AdvancedContentAnalyzer
from performance_technical_analyzer import PerformanceTechnicalAnalyzer
from parallel_processing_system import ParallelProcessingSystem, ProcessingTask, ProcessingMode
from performance_monitoring_system import PerformanceMonitor, MetricType

# Try to import caching (optional)
try:
    from enhancement_cache import EnhancementCache, CacheLevel
    CACHE_AVAILABLE = True
except ImportError:
    CACHE_AVAILABLE = False

logger = logging.getLogger(__name__)

class EnhancedScraperPipelinePhase3:
    """
    Enhanced scraper pipeline with Phase 3 advanced features
    """
    
    def __init__(self, api_key: str):
        self.logger = logging.getLogger(__name__)
        
        # Initialize existing services
        self.ai_client = AINavigatorClient()
        self.enrichment_service = DataEnrichmentService(api_key)
        self.taxonomy_service = EnhancedTaxonomyService(self.ai_client)
        
        # Initialize Phase 3 components
        self.structured_extractor = StructuredDataExtractor()
        self.content_analyzer = AdvancedContentAnalyzer(api_key)
        self.performance_analyzer = PerformanceTechnicalAnalyzer()
        self.parallel_processor = ParallelProcessingSystem(max_workers=4)
        self.performance_monitor = PerformanceMonitor()
        
        # Initialize caching if available
        if CACHE_AVAILABLE:
            self.cache = EnhancementCache()
            self.logger.info("✅ Cache system initialized")
        else:
            self.cache = None
            self.logger.warning("⚠️  Cache system not available - proceeding without caching")
        
        # Start performance monitoring
        self.performance_monitor.start_system_monitoring(interval=30.0)
        
        # Pipeline statistics
        self.stats = {
            'total_processed': 0,
            'successful_enhancements': 0,
            'failed_enhancements': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'processing_times': [],
            'phase3_metrics': {
                'structured_data_elements': 0,
                'content_analysis_elements': 0,
                'performance_metrics': 0
            }
        }
        
        self.logger.info("🚀 Enhanced Scraper Pipeline Phase 3 initialized")

    def process_tools_enhanced(self, tools: List[Dict[str, Any]], 
                              use_parallel: bool = True) -> Dict[str, Any]:
        """
        Process multiple tools with Phase 3 enhancements
        
        Args:
            tools: List of tool dictionaries with 'name' and 'url'
            use_parallel: Whether to use parallel processing
            
        Returns:
            Processing results and statistics
        """
        start_time = time.time()
        self.logger.info(f"🔄 Starting enhanced processing of {len(tools)} tools")
        
        # Record start metric
        self.performance_monitor.record_metric("batch_processing_start", len(tools), MetricType.COUNTER)
        
        # Create processing tasks
        tasks = []
        for i, tool in enumerate(tools):
            task = ProcessingTask(
                task_id=f"tool_{i}",
                tool_name=tool.get('name', f'Tool_{i}'),
                url=tool.get('url', ''),
                data=tool,
                priority=1  # High priority
            )
            tasks.append(task)
        
        # Process tasks
        if use_parallel and len(tasks) > 1:
            self.logger.info("🔀 Using parallel processing mode")
            results = self.parallel_processor.process_batch(
                tasks, 
                self._process_single_tool_enhanced,
                ProcessingMode.HYBRID
            )
        else:
            self.logger.info("➡️  Using sequential processing mode")
            results = self.parallel_processor.process_batch(
                tasks,
                self._process_single_tool_enhanced,
                ProcessingMode.SEQUENTIAL
            )
        
        # Process results and update statistics
        successful_results = []
        failed_results = []
        
        for result in results:
            if result.success:
                successful_results.append(result)
                self.stats['successful_enhancements'] += 1
                self.stats['processing_times'].append(result.processing_time)
            else:
                failed_results.append(result)
                self.stats['failed_enhancements'] += 1
        
        self.stats['total_processed'] = len(results)
        
        # Calculate final statistics
        total_time = time.time() - start_time
        avg_processing_time = sum(self.stats['processing_times']) / len(self.stats['processing_times']) if self.stats['processing_times'] else 0
        
        # Record completion metrics
        self.performance_monitor.record_metric("batch_processing_complete", len(results), MetricType.COUNTER)
        self.performance_monitor.record_metric("batch_processing_time", total_time, MetricType.TIMER)
        self.performance_monitor.record_metric("avg_tool_processing_time", avg_processing_time, MetricType.GAUGE)
        
        # Generate comprehensive report
        report = {
            'success': True,
            'total_tools': len(tools),
            'successful_enhancements': len(successful_results),
            'failed_enhancements': len(failed_results),
            'success_rate': (len(successful_results) / len(tools)) * 100 if tools else 0,
            'total_processing_time': total_time,
            'average_processing_time': avg_processing_time,
            'cache_stats': {
                'hits': self.stats['cache_hits'],
                'misses': self.stats['cache_misses'],
                'hit_rate': (self.stats['cache_hits'] / (self.stats['cache_hits'] + self.stats['cache_misses'])) * 100 if (self.stats['cache_hits'] + self.stats['cache_misses']) > 0 else 0
            },
            'phase3_metrics': self.stats['phase3_metrics'],
            'performance_summary': self.performance_monitor.get_performance_dashboard(),
            'successful_results': [asdict(r) for r in successful_results],
            'failed_results': [asdict(r) for r in failed_results]
        }
        
        self.logger.info(f"✅ Enhanced processing complete: {len(successful_results)}/{len(tools)} successful")
        return report

    def _process_single_tool_enhanced(self, task: ProcessingTask) -> Dict[str, Any]:
        """
        Process a single tool with all Phase 3 enhancements
        """
        tool_name = task.tool_name
        url = task.url
        
        timer_id = self.performance_monitor.start_timer(f"tool_processing_{tool_name}")
        
        try:
            self.logger.info(f"🔍 Processing: {tool_name}")
            self.logger.info(f"   URL: {url}")
            
            # Check cache first
            cached_result = None
            if self.cache:
                cached_result = self.cache.get_cached_enhancement(f"{tool_name}_{url}")
                if cached_result:
                    self.stats['cache_hits'] += 1
                    self.logger.info(f"   💾 Cache hit for {tool_name}")
                    return cached_result
                else:
                    self.stats['cache_misses'] += 1
            
            # Step 1: Scrape website content
            website_data = self._scrape_website_content(url)
            html_content = website_data.get('html_content', '')
            
            # Step 2: Phase 3 Advanced Analysis
            phase3_results = self._run_phase3_analysis(url, html_content)
            
            # Step 3: Traditional AI Enhancement
            ai_enhanced_data = self.enrichment_service.enrich_tool_data(tool_name, url)
            
            # Step 4: Merge all enhancement data
            comprehensive_data = self._merge_enhancement_data(
                tool_name, url, website_data, ai_enhanced_data, phase3_results
            )
            
            # Step 5: Apply taxonomy mapping
            taxonomy_data = self._apply_taxonomy_mapping(comprehensive_data)
            comprehensive_data.update(taxonomy_data)
            
            # Step 6: Create final entity for database
            entity_data = self._create_database_entity(comprehensive_data)
            
            # Step 7: Save to database
            database_result = self._save_to_database(entity_data)
            
            # Step 8: Cache the result
            if self.cache:
                self.cache.cache_enhancement_result(
                    f"{tool_name}_{url}", 
                    comprehensive_data,
                    CacheLevel.MEDIUM_TERM
                )
            
            # Update Phase 3 statistics
            self.stats['phase3_metrics']['structured_data_elements'] += len(phase3_results['structured_data'])
            self.stats['phase3_metrics']['content_analysis_elements'] += len(phase3_results['content_analysis'])
            self.stats['phase3_metrics']['performance_metrics'] += len(phase3_results['performance_analysis'])
            
            processing_time = self.performance_monitor.stop_timer(timer_id)
            
            result = {
                'tool_name': tool_name,
                'url': url,
                'success': True,
                'database_result': database_result,
                'enhancement_data': comprehensive_data,
                'phase3_results': phase3_results,
                'processing_time': processing_time,
                'cached': False
            }
            
            self.logger.info(f"   ✅ Successfully processed {tool_name} in {processing_time:.2f}s")
            return result
            
        except Exception as e:
            processing_time = self.performance_monitor.stop_timer(timer_id)
            self.logger.error(f"   ❌ Error processing {tool_name}: {str(e)}")
            
            return {
                'tool_name': tool_name,
                'url': url,
                'success': False,
                'error': str(e),
                'processing_time': processing_time
            }

    def _scrape_website_content(self, url: str) -> Dict[str, Any]:
        """Scrape website content for analysis"""
        try:
            import requests
            from bs4 import BeautifulSoup
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            response = requests.get(url, headers=headers, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            return {
                'html_content': response.text,
                'title': soup.title.string if soup.title else '',
                'meta_description': soup.find('meta', attrs={'name': 'description'}),
                'status_code': response.status_code,
                'content_length': len(response.content)
            }
            
        except Exception as e:
            self.logger.warning(f"Error scraping {url}: {str(e)}")
            return {'html_content': '', 'error': str(e)}

    def _run_phase3_analysis(self, url: str, html_content: str) -> Dict[str, Any]:
        """Run all Phase 3 analysis components"""
        phase3_results = {
            'structured_data': [],
            'content_analysis': [],
            'performance_analysis': []
        }
        
        try:
            # Structured Data Extraction
            structured_results = self.structured_extractor.extract_all_structured_data(url, html_content)
            phase3_results['structured_data'] = [asdict(r) for r in structured_results]
            
            # Advanced Content Analysis
            content_results = self.content_analyzer.analyze_content(url, html_content)
            phase3_results['content_analysis'] = [asdict(r) for r in content_results]
            
            # Performance and Technical Analysis
            performance_results = self.performance_analyzer.analyze_performance_and_technical(url, html_content)
            phase3_results['performance_analysis'] = [asdict(r) for r in performance_results]
            
            self.logger.info(f"   📊 Phase 3 Analysis: {len(structured_results)} structured, {len(content_results)} content, {len(performance_results)} performance")
            
        except Exception as e:
            self.logger.error(f"Error in Phase 3 analysis: {str(e)}")
        
        return phase3_results

    def _merge_enhancement_data(self, tool_name: str, url: str, website_data: Dict, 
                               ai_data: Dict, phase3_data: Dict) -> Dict[str, Any]:
        """Merge all enhancement data into comprehensive dataset"""
        
        # Start with AI-enhanced data as base
        comprehensive_data = ai_data.copy()
        
        # Add basic information
        comprehensive_data.update({
            'name': tool_name,
            'website_url': url,
            'scraped_title': website_data.get('title', ''),
            'content_length': website_data.get('content_length', 0)
        })
        
        # Add Phase 3 structured data insights
        structured_data = phase3_data.get('structured_data', [])
        if structured_data:
            # Extract pricing information from structured data
            pricing_elements = [s for s in structured_data if s.get('data_type') == 'pricing_table']
            if pricing_elements:
                comprehensive_data['structured_pricing_found'] = True
                comprehensive_data['pricing_confidence'] = max(p.get('confidence', 0) for p in pricing_elements)
        
        # Add Phase 3 content analysis insights
        content_analysis = phase3_data.get('content_analysis', [])
        if content_analysis:
            testimonials = [c for c in content_analysis if c.get('content_type') == 'testimonial']
            selling_points = [c for c in content_analysis if c.get('content_type') == 'selling_point']
            
            comprehensive_data['testimonials_found'] = len(testimonials)
            comprehensive_data['selling_points_found'] = len(selling_points)
            
            if testimonials:
                comprehensive_data['customer_testimonials'] = [t.get('content', '') for t in testimonials[:3]]
        
        # Add Phase 3 performance insights
        performance_analysis = phase3_data.get('performance_analysis', [])
        if performance_analysis:
            mobile_friendly = [p for p in performance_analysis if p.get('metric_type') == 'mobile_friendly']
            if mobile_friendly:
                comprehensive_data['mobile_friendly_score'] = mobile_friendly[0].get('score', 0)
        
        # Add Phase 3 raw data for reference
        comprehensive_data['phase3_analysis'] = phase3_data
        
        return comprehensive_data

    def _apply_taxonomy_mapping(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Apply taxonomy mapping using enhanced service"""
        try:
            categories = data.get('categories', [])
            tags = data.get('tags', [])
            features = data.get('key_features', [])
            
            # Map using enhanced taxonomy service
            category_ids = self.taxonomy_service.map_categories(categories)
            tag_ids = self.taxonomy_service.map_tags(tags)
            feature_ids = self.taxonomy_service.map_features(features)
            
            # CRITICAL FIX: Ensure we always have at least one category and tag
            # The AI Navigator API requires at least 1 element in each array
            if not category_ids:
                self.logger.warning("No categories mapped, using default category")
                default_category_id = self._get_default_category_id()
                if default_category_id:
                    category_ids = [default_category_id]
                else:
                    self.logger.error("No default category available!")

            if not tag_ids:
                self.logger.warning("No tags mapped, using default tag")
                default_tag_id = self._get_default_tag_id()
                if default_tag_id:
                    tag_ids = [default_tag_id]
                else:
                    self.logger.error("No default tag available!")

            self.logger.info(f"Final taxonomy mapping: {len(category_ids)} categories, {len(tag_ids)} tags, {len(feature_ids)} features")

            return {
                'category_ids': category_ids,
                'tag_ids': tag_ids,
                'feature_ids': feature_ids
            }

        except Exception as e:
            self.logger.error(f"Error in taxonomy mapping: {str(e)}")
            # Return defaults even on error
            return {
                'category_ids': [self._get_default_category_id()] if self._get_default_category_id() else [],
                'tag_ids': [self._get_default_tag_id()] if self._get_default_tag_id() else [],
                'feature_ids': []
            }

    def _create_database_entity(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Create entity data structure for database submission"""

        # Get the AI tool entity type ID (CRITICAL - this was missing!)
        entity_type_id = self.ai_client.get_ai_tool_entity_type_id()
        if not entity_type_id:
            self.logger.error("❌ Could not get AI tool entity type ID - this will cause database save failures")
            # Use a fallback or raise an exception
            raise ValueError("AI tool entity type ID not found - cannot create entities")

        # Create comprehensive entity following AI Navigator schema
        # IMPORTANT: Only include fields that exist in the actual API schema
        entity_data = {
            'name': data.get('name', ''),
            'entity_type_id': entity_type_id,  # CRITICAL FIELD - was missing!
            'short_description': data.get('short_description', '')[:500],
            'description': data.get('description', '')[:2000],
            'website_url': data.get('website_url', ''),
            'logo_url': data.get('logo_url'),
            'category_ids': data.get('category_ids', []),
            'tag_ids': data.get('tag_ids', []),
            'feature_ids': data.get('feature_ids', []),

            # Required metadata fields for AI Navigator
            'meta_title': f"{data.get('name', '')} | AI Navigator",
            'meta_description': data.get('short_description', '')[:160],
            'ref_link': data.get('website_url', ''),
            'affiliate_status': 'NONE',
            'status': 'ACTIVE',

            # Additional URLs discovered (only if they exist in schema)
            'documentation_url': data.get('documentation_url'),
            'contact_url': data.get('contact_url'),
            'privacy_policy_url': data.get('privacy_policy_url'),
            # Note: pricing_url removed as it doesn't exist in the API schema
        }

        # Note: Removed these fields as they don't exist in the API schema:
        # - pricing_model, price_range, technical_level, has_free_tier
        # - testimonials_found, selling_points_found, structured_pricing_found
        # - mobile_friendly_score

        # Remove None values but keep empty arrays/strings
        entity_data = {k: v for k, v in entity_data.items() if v is not None}

        # Validate required fields
        required_fields = ['name', 'entity_type_id', 'website_url']
        missing_fields = [field for field in required_fields if not entity_data.get(field)]
        if missing_fields:
            self.logger.error(f"❌ Missing required fields for entity creation: {missing_fields}")
            raise ValueError(f"Missing required fields: {missing_fields}")

        self.logger.debug(f"Created entity data with entity_type_id: {entity_type_id}")
        return entity_data

    def _get_default_category_id(self) -> Optional[str]:
        """Get a default category ID for tools without specific categories"""
        try:
            # Try to get categories from the AI Navigator API
            categories = self.ai_client.get_categories()

            # Look for common AI/tool categories
            preferred_categories = [
                'ai tools', 'artificial intelligence', 'productivity',
                'general', 'ai & machine learning', 'tools', 'software'
            ]

            for category in categories:
                category_name = category.get('name', '').lower()
                if category_name in preferred_categories:
                    category_id = category.get('id')
                    # Validate UUID format
                    if self._is_valid_uuid(category_id):
                        self.logger.info(f"Using default category: {category.get('name')} ({category_id})")
                        return category_id

            # If no preferred category found, use the first available with valid UUID
            if categories:
                for category in categories:
                    category_id = category.get('id')
                    if self._is_valid_uuid(category_id):
                        self.logger.info(f"Using first available category: {category.get('name')} ({category_id})")
                        return category_id

        except Exception as e:
            self.logger.error(f"Error getting default category: {str(e)}")

        return None

    def _get_default_tag_id(self) -> Optional[str]:
        """Get a default tag ID for tools without specific tags"""
        try:
            # Try to get tags from the AI Navigator API
            tags = self.ai_client.get_tags()

            # Look for common default tags
            preferred_tags = [
                'ai-powered', 'free tier', 'productivity', 'general',
                'tool', 'software', 'web-based', 'professional'
            ]

            for tag in tags:
                tag_name = tag.get('name', '').lower()
                if tag_name in preferred_tags:
                    tag_id = tag.get('id')
                    # Validate UUID format
                    if self._is_valid_uuid(tag_id):
                        self.logger.info(f"Using default tag: {tag.get('name')} ({tag_id})")
                        return tag_id

            # If no preferred tag found, use the first available with valid UUID
            if tags:
                for tag in tags:
                    tag_id = tag.get('id')
                    if self._is_valid_uuid(tag_id):
                        self.logger.info(f"Using first available tag: {tag.get('name')} ({tag_id})")
                        return tag_id

        except Exception as e:
            self.logger.error(f"Error getting default tag: {str(e)}")

        return None

    def _is_valid_uuid(self, uuid_string: str) -> bool:
        """Check if a string is a valid UUID and not a test/placeholder UUID"""
        try:
            import uuid
            parsed_uuid = uuid.UUID(str(uuid_string))

            # Reject suspicious test/placeholder UUIDs that the API doesn't accept
            if uuid_string.startswith('*************-0000-0000-'):
                self.logger.warning(f"Rejecting suspicious test UUID: {uuid_string}")
                return False

            return True
        except (ValueError, TypeError):
            return False

    def _save_to_database(self, entity_data: Dict[str, Any]) -> Dict[str, Any]:
        """Save entity to AI Navigator database"""
        try:
            result = self.ai_client.create_entity(entity_data)
            
            if result:
                self.logger.info(f"   💾 Successfully saved to database: {entity_data.get('name')}")
                return {'success': True, 'entity_id': result.get('id'), 'result': result}
            else:
                self.logger.error(f"   ❌ Failed to save to database: {entity_data.get('name')}")
                return {'success': False, 'error': 'Database submission failed'}
                
        except Exception as e:
            self.logger.error(f"   ❌ Database error: {str(e)}")
            return {'success': False, 'error': str(e)}

    def get_comprehensive_stats(self) -> Dict[str, Any]:
        """Get comprehensive pipeline statistics"""
        return {
            'pipeline_stats': self.stats,
            'parallel_processing_stats': self.parallel_processor.get_processing_stats(),
            'performance_dashboard': self.performance_monitor.get_performance_dashboard(),
            'cache_stats': self.cache.get_cache_stats() if self.cache else None
        }

    def cleanup(self):
        """Cleanup resources"""
        self.performance_monitor.stop_system_monitoring()
        if self.cache:
            self.cache.cleanup_expired_entries()
        self.logger.info("🧹 Pipeline cleanup complete")
