#!/bin/bash

# Start Enhanced AI Navigator Scrapers System
# This script starts both the enhanced backend and frontend

echo "🚀 Starting Enhanced AI Navigator Scrapers System"
echo "=================================================="

# Check if we're in the right directory
if [ ! -f "backend/enhanced_server_phase3.py" ]; then
    echo "❌ Error: Please run this script from the project root directory"
    echo "   Current directory: $(pwd)"
    echo "   Expected files: backend/enhanced_server_phase3.py"
    exit 1
fi

# Function to check if port is in use
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null ; then
        return 0  # Port is in use
    else
        return 1  # Port is free
    fi
}

# Check if backend port is already in use
if check_port 8001; then
    echo "⚠️  Port 8001 is already in use. Stopping existing backend..."
    pkill -f "enhanced_server_phase3.py" 2>/dev/null || true
    sleep 2
fi

# Check if frontend port is already in use
if check_port 3000; then
    echo "⚠️  Port 3000 is already in use. Frontend may already be running."
fi

echo ""
echo "🔧 Starting Enhanced Backend Server..."
echo "   URL: http://localhost:8001"
echo "   Features: Phase 3 Enhanced Pipeline"

# Start backend in background
python3 backend/enhanced_server_phase3.py &
BACKEND_PID=$!

# Wait a moment for backend to start
sleep 3

# Check if backend started successfully
if check_port 8001; then
    echo "   ✅ Enhanced backend server started successfully (PID: $BACKEND_PID)"
else
    echo "   ❌ Failed to start enhanced backend server"
    exit 1
fi

echo ""
echo "🌐 Starting Frontend Development Server..."
echo "   URL: http://localhost:3000"
echo "   Features: Enhanced UI with Phase 3 Integration"

# Change to frontend directory and start
cd frontend

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "   📦 Installing frontend dependencies..."
    npm install
fi

# Start frontend
echo "   🚀 Starting React development server..."
npm start &
FRONTEND_PID=$!

# Wait for frontend to start
sleep 5

echo ""
echo "🎉 Enhanced AI Navigator Scrapers System Started!"
echo "=================================================="
echo ""
echo "📊 System Status:"
echo "   ✅ Enhanced Backend: http://localhost:8001"
echo "   ✅ Frontend UI: http://localhost:3000"
echo ""
echo "🚀 Phase 3 Features Available:"
echo "   ✅ Structured Data Extraction"
echo "   ✅ Advanced Content Analysis"
echo "   ✅ Performance & Technical Analysis"
echo "   ✅ Parallel Processing (1.6x faster)"
echo "   ✅ Real-time Monitoring"
echo "   ✅ Performance Dashboard"
echo ""
echo "🎯 Next Steps:"
echo "   1. Open http://localhost:3000 in your browser"
echo "   2. Click on the 'Enhanced' tab"
echo "   3. Configure tools and start enhanced processing"
echo "   4. Monitor real-time progress and results"
echo ""
echo "⚠️  To stop the system:"
echo "   Press Ctrl+C or run: pkill -f 'enhanced_server_phase3.py' && pkill -f 'react-scripts'"
echo ""

# Keep script running to show logs
echo "📋 System Logs (Press Ctrl+C to stop):"
echo "======================================="

# Wait for user to stop
wait
