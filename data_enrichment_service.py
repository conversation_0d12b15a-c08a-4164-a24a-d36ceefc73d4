"""
Data Enrichment Service using Perplexity API
Enhances basic scraped data with additional information about AI tools.
"""

import requests
import logging
import re
from typing import Dict, Any, Optional, List
from urllib.parse import urlparse
import json
from config import config
from technical_level_classifier import TechnicalLevelClassifier
from logo_extraction_service import LogoExtractionService
from url_discovery_service import URLDiscoveryService

class DataEnrichmentService:
    def __init__(self, perplexity_api_key: str):
        self.api_key = perplexity_api_key
        self.base_url = "https://api.perplexity.ai/chat/completions"
        self.logger = logging.getLogger(__name__)

        # Initialize technical level classifier
        self.technical_classifier = TechnicalLevelClassifier(perplexity_api_key)

        # Initialize logo extraction service
        self.logo_extractor = LogoExtractionService(perplexity_api_key)

        # Initialize URL discovery service
        self.url_discoverer = URLDiscoveryService(perplexity_api_key)
        
    def _call_perplexity(self, prompt: str, max_tokens: int = 1000) -> Optional[str]:
        """Make a call to Perplexity API"""
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": "llama-3.1-sonar-small-128k-online",
                "messages": [
                    {
                        "role": "system",
                        "content": "You are an expert AI tool researcher building the world's most comprehensive AI tool directory. Provide specific, detailed, and searchable information about AI tools and software. Focus on unique capabilities, concrete use cases, and actionable features. Avoid generic descriptions. Always research thoroughly and provide only verified, factual information in the exact JSON format requested."
                    },
                    {
                        "role": "user", 
                        "content": prompt
                    }
                ],
                "max_tokens": max_tokens,
                "temperature": 0.2,
                "stream": False
            }
            
            response = requests.post(
                self.base_url,
                headers=headers,
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content']
            else:
                self.logger.error(f"Perplexity API error: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error calling Perplexity API: {str(e)}")
            return None
    
    def enrich_tool_data(self, tool_name: str, website_url: str, basic_description: str = "") -> Dict[str, Any]:
        """Enrich basic tool information with comprehensive detailed data"""
        
        # Create a highly structured prompt with explicit JSON schema for maximum data quality
        enhanced_prompt = f"""
        You are building the world's most comprehensive AI tool directory. Research "{tool_name}" (website: {website_url}) thoroughly and provide detailed, searchable information.

        CRITICAL REQUIREMENTS:
        1. Make descriptions SPECIFIC and UNIQUE - avoid generic phrases like "AI-powered tool" or "enhance productivity"
        2. Focus on SEARCHABILITY - users will search for specific features, use cases, and capabilities
        3. Provide ACTIONABLE details - what exactly can users do with this tool?
        4. Be COMPREHENSIVE but ACCURATE - research thoroughly but only include verified information

        Return ONLY valid JSON in this EXACT format:

        {
            "short_description": "[REQUIRED: 120-160 chars] Compelling, specific value proposition that clearly explains what this tool does differently. Avoid generic phrases like 'AI-powered tool' or 'enhance productivity'. Example: 'Converts natural language into SQL queries with 95% accuracy for non-technical users'",

            "description": "[REQUIRED: 300-500 words] Comprehensive description that covers: (1) Core functionality and unique capabilities, (2) Target use cases with specific examples, (3) Key differentiators from competitors, (4) Technical approach or methodology, (5) Benefits and outcomes users can expect. Make it searchable - include terms users would search for.",

            "key_features": [
                "[REQUIRED: 5-8 features] List specific, actionable features users can perform. Focus on WHAT users can do, not generic benefits. Examples: 'Generate SQL from natural language', 'Real-time collaboration on queries', 'Connect to 50+ databases', 'Visual query builder', 'Automated performance optimization'"
            ],

            "use_cases": [
                "[REQUIRED: 6-8 use cases] Specific, concrete scenarios where users would use this tool. Each use case should include: WHO uses it, WHAT problem they solve, HOW the tool helps, and WHAT outcome they achieve. Examples: 'E-commerce managers tracking abandoned cart recovery campaigns to increase conversion rates by 15%', 'Content creators generating social media posts in brand voice to maintain consistency across platforms', 'Sales teams building automated lead scoring models to prioritize high-value prospects'"
            ],

            "target_audience": [
                "[REQUIRED: 3-5 audiences] Specific user types, not generic terms. Examples: 'Data analysts at mid-size companies', 'Marketing managers without technical background', 'Product teams at SaaS startups', 'Business intelligence professionals'"
            ],

            "categories": [
                "[REQUIRED: 1-3 categories] Choose from: Data Analytics, Business Intelligence, Database Tools, SQL Tools, No-Code/Low-Code, Data Visualization, ETL/Data Pipeline, Machine Learning, AI Assistant, Productivity, Developer Tools, Marketing Tools, Sales Tools, Customer Support, Content Creation, Image Generation, Video Editing, Audio Processing, Text Analysis, Code Generation, API Tools, Automation, Integration Platform, Project Management, Collaboration, Design Tools, Prototyping, Testing Tools, Security, Monitoring, DevOps"
            ],

            "tags": [
                "[REQUIRED: 7-10 tags] Specific, searchable keywords users would look for. Include: (1) Primary function tags, (2) Technology/method tags, (3) Industry/use case tags, (4) User type tags. Examples: 'SQL generation', 'natural language processing', 'database connectivity', 'business intelligence', 'no-code', 'real-time collaboration', 'data visualization', 'marketing automation', 'sales enablement', 'enterprise-ready'"
            ],

            "pricing_model": "FREE|FREEMIUM|SUBSCRIPTION|PAY_PER_USE|ONE_TIME_PURCHASE|CONTACT_SALES|OPEN_SOURCE",
            "price_range": "FREE|LOW|MEDIUM|HIGH|ENTERPRISE",
            "pricing_details": "[Specific pricing information with actual numbers if available. Example: 'Free tier: 100 queries/month. Pro: $29/month for unlimited queries. Enterprise: Custom pricing for teams 50+']",
            "has_free_tier": true/false,

            "integrations": ["[List specific tools/platforms this integrates with. Examples: 'Slack', 'Microsoft Teams', 'Salesforce', 'HubSpot', 'PostgreSQL', 'MySQL', 'Snowflake']"],
            "api_access": true/false,
            "mobile_support": true/false,
            "technical_level": "BEGINNER|INTERMEDIATE|ADVANCED|EXPERT",
            "learning_curve": "EASY|MEDIUM|HARD - Based on how quickly users can become productive with this tool. EASY: Can start using effectively within 1 hour. MEDIUM: Requires 1-8 hours to become proficient. HARD: Requires significant training or technical expertise.",

            "logo_url": "[Direct URL to high-quality logo image if found]",
            "documentation_url": "[URL to documentation/help pages]",
            "contact_url": "[URL to contact/support page]",
            "privacy_policy_url": "[URL to privacy policy]",
            "pricing_url": "[URL to pricing page]",
            "support_email": "[Support email address if found]",
            "has_live_chat": true/false,
            "community_url": "[URL to Discord, Slack, Reddit, or other community if found]",
            "founded_year": "[Year founded if available]",
            "social_links": {
                "twitter": "[Twitter/X URL if found]",
                "linkedin": "[LinkedIn URL if found]",
                "github": "[GitHub URL if found]",
                "discord": "[Discord URL if found]"
            }
        }

        ENHANCED QUALITY GUIDELINES:
        1. SPECIFICITY: Replace generic terms with specific capabilities. Instead of "AI-powered", say "Uses GPT-4 for natural language processing" or "Machine learning algorithms for predictive analytics"

        2. SEARCHABILITY: Include terms users would actually search for. Think about pain points and solutions. Examples: "convert text to speech", "automated social media posting", "real-time data synchronization"

        3. UNIQUENESS: Highlight what makes this tool different from competitors. What's their unique approach, technology, or methodology?

        4. ACTIONABLE FEATURES: Focus on what users can DO, not abstract benefits. "Generate 50 social media posts from one blog article" vs "Enhance content creation"

        5. CONCRETE USE CASES: Provide specific scenarios with context. Include WHO, WHAT problem, HOW solved, WHAT outcome. "E-commerce managers tracking abandoned cart recovery campaigns to increase conversion rates by 15%" vs "Business analytics"

        6. COMPREHENSIVE TAGS: Include functional tags (what it does), technical tags (how it works), industry tags (who uses it), and outcome tags (what results it delivers)

        7. LEARNING CURVE ASSESSMENT: Consider the tool's complexity, onboarding process, documentation quality, and typical user feedback to accurately assess learning curve

        8. CONTACT INFORMATION PRIORITY: Actively search for and include support email, live chat availability, and community links as these are high-value for users

        9. ACCURACY: Only include verified information. Use null for unknown fields. Research the actual website and available documentation.

        10. PROFESSIONAL TONE: Write for professionals evaluating tools for their business needs.

        Return ONLY the JSON object, no additional text or formatting.
        """
        
        prompt = enhanced_prompt
        
        response = self._call_perplexity(prompt, max_tokens=2000)
        
        if response:
            try:
                # Extract JSON from response
                json_match = re.search(r'\{.*\}', response, re.DOTALL)
                if json_match:
                    enriched_data = json.loads(json_match.group())

                    # Add technical level classification
                    technical_level = self.technical_classifier.classify_technical_level(enriched_data)
                    enriched_data['technical_level'] = technical_level

                    # Add classification confidence
                    confidence = self.technical_classifier.get_classification_confidence(enriched_data, technical_level)
                    enriched_data['technical_level_confidence'] = confidence

                    # Extract logo URL
                    logo_url = self.logo_extractor.extract_logo_url(website_url)
                    if logo_url:
                        enriched_data['logo_url'] = logo_url
                        logo_quality = self.logo_extractor.get_logo_quality_score(logo_url)
                        enriched_data['logo_quality_score'] = logo_quality
                        self.logger.info(f"Logo extracted for {tool_name}: {logo_url} (quality: {logo_quality:.2f})")
                    else:
                        enriched_data['logo_url'] = None
                        enriched_data['logo_quality_score'] = 0.0

                    # Discover additional URLs
                    discovered_urls = self.url_discoverer.discover_urls(website_url)
                    enriched_data.update(discovered_urls)

                    url_count = len([url for url in discovered_urls.values() if url])
                    self.logger.info(f"Discovered {url_count} additional URLs for {tool_name}")

                    self.logger.info(f"Successfully enriched data for {tool_name} with technical level: {technical_level} (confidence: {confidence:.2f})")
                    return enriched_data
                else:
                    self.logger.warning(f"Could not extract JSON from Perplexity response for {tool_name}")
                    return self._fallback_enrichment(tool_name, website_url, basic_description)
                    
            except json.JSONDecodeError as e:
                self.logger.error(f"Failed to parse JSON response for {tool_name}: {str(e)}")
                return self._fallback_enrichment(tool_name, website_url, basic_description)
        
        return self._fallback_enrichment(tool_name, website_url, basic_description)
    
    def _fallback_enrichment(self, tool_name: str, website_url: str, basic_description: str) -> Dict[str, Any]:
        """Provide basic fallback data when API enrichment fails"""
        domain = urlparse(website_url).netloc.lower()
        
        # Basic categorization based on tool name patterns
        categories = []
        tags = []
        
        name_lower = tool_name.lower()
        if any(word in name_lower for word in ['chat', 'gpt', 'ai']):
            categories.append('AI Assistant')
            tags.extend(['AI', 'Assistant'])
        
        if any(word in name_lower for word in ['write', 'content', 'copy']):
            categories.append('Content Creation')
            tags.extend(['Writing', 'Content'])
            
        if any(word in name_lower for word in ['image', 'photo', 'visual']):
            categories.append('Image Generation')
            tags.extend(['Image', 'Visual'])
        
        # Create more specific fallback data based on tool name and URL analysis
        domain_hints = self._analyze_domain_for_hints(website_url)
        name_hints = self._analyze_name_for_hints(tool_name)

        fallback_data = {
            "short_description": basic_description or f"{tool_name} - {domain_hints.get('description', 'Specialized AI tool for business automation and workflow optimization')}",
            "description": basic_description or f"{tool_name} is a specialized tool that {domain_hints.get('functionality', 'provides AI-powered solutions for business needs')}. The platform offers {name_hints.get('capabilities', 'advanced features')} designed to {domain_hints.get('purpose', 'streamline workflows and improve operational efficiency')}. Users can leverage this tool to {name_hints.get('use_case', 'automate complex tasks and enhance productivity')} while maintaining {domain_hints.get('benefit', 'high accuracy and reliability')}.",
            "key_features": name_hints.get('features', ["Advanced automation capabilities", "Intuitive user interface", "Real-time processing", "Customizable workflows", "Integration support"]),
            "use_cases": domain_hints.get('use_cases', ["Process automation for business operations", "Data analysis and reporting", "Workflow optimization", "Task management and scheduling"]),
            "pricing_model": "FREEMIUM",
            "price_range": "MEDIUM",
            "pricing_details": "Visit website for current pricing information and available plans",
            "target_audience": domain_hints.get('audience', ["Business professionals", "Operations managers", "Technical teams"]),
            "categories": categories if categories else domain_hints.get('categories', ["Productivity Tools"]),
            "tags": tags if tags else name_hints.get('tags', ["automation", "business tools", "workflow"]),
            "has_free_tier": True,
            "api_access": False,
            "mobile_support": False,
            "integrations": [],
            "founded_year": None,
            "employee_count_range": "Unknown",
            "funding_stage": "Unknown"
        }

        # Add technical level classification even for fallback data
        technical_level = self.technical_classifier.classify_technical_level(fallback_data)
        fallback_data['technical_level'] = technical_level
        fallback_data['technical_level_confidence'] = 0.5  # Lower confidence for fallback

        # Try to extract logo even for fallback
        logo_url = self.logo_extractor.extract_logo_url(website_url)
        if logo_url:
            fallback_data['logo_url'] = logo_url
            fallback_data['logo_quality_score'] = self.logo_extractor.get_logo_quality_score(logo_url)
        else:
            fallback_data['logo_url'] = None
            fallback_data['logo_quality_score'] = 0.0

        # Try to discover URLs even for fallback
        discovered_urls = self.url_discoverer.discover_urls(website_url)
        fallback_data.update(discovered_urls)

        return fallback_data

    def _analyze_domain_for_hints(self, website_url: str) -> Dict[str, Any]:
        """Analyze domain/URL for contextual hints to improve fallback data"""
        domain = website_url.lower()

        # Domain-based categorization
        if any(term in domain for term in ['chat', 'gpt', 'ai', 'bot']):
            return {
                'description': 'AI-powered conversational tool for automated interactions',
                'functionality': 'enables intelligent conversations and automated responses',
                'purpose': 'enhance customer engagement and support',
                'benefit': 'natural language understanding',
                'use_cases': ['Customer support automation', 'Chatbot development', 'Conversational AI implementation'],
                'audience': ['Customer service teams', 'Developers', 'Business owners'],
                'categories': ['AI Assistant', 'Customer Support'],
            }
        elif any(term in domain for term in ['design', 'create', 'generate', 'art']):
            return {
                'description': 'Creative AI tool for design and content generation',
                'functionality': 'generates visual content and design assets',
                'purpose': 'accelerate creative workflows',
                'benefit': 'professional-quality output',
                'use_cases': ['Graphic design automation', 'Marketing asset creation', 'Brand identity development'],
                'audience': ['Designers', 'Marketing teams', 'Content creators'],
                'categories': ['Design Tools', 'Content Creation'],
            }
        elif any(term in domain for term in ['data', 'analytics', 'insight', 'report']):
            return {
                'description': 'Data analytics and business intelligence platform',
                'functionality': 'processes and analyzes business data',
                'purpose': 'drive data-driven decision making',
                'benefit': 'actionable insights',
                'use_cases': ['Business performance analysis', 'Data visualization', 'Predictive analytics'],
                'audience': ['Data analysts', 'Business managers', 'Decision makers'],
                'categories': ['Data Analytics', 'Business Intelligence'],
            }
        else:
            return {
                'description': 'Professional business automation tool',
                'functionality': 'streamlines business processes',
                'purpose': 'improve operational efficiency',
                'benefit': 'increased productivity',
                'use_cases': ['Business process automation', 'Workflow optimization', 'Task management'],
                'audience': ['Business professionals', 'Operations teams'],
                'categories': ['Productivity Tools'],
            }

    def _analyze_name_for_hints(self, tool_name: str) -> Dict[str, Any]:
        """Analyze tool name for contextual hints"""
        name = tool_name.lower()

        # Name-based feature inference
        if any(term in name for term in ['chat', 'gpt', 'bot', 'assistant']):
            return {
                'capabilities': 'conversational AI and natural language processing',
                'use_case': 'engage in intelligent conversations and provide automated assistance',
                'features': ['Natural language understanding', 'Contextual responses', 'Multi-turn conversations', 'Intent recognition', 'Response generation'],
                'tags': ['conversational AI', 'chatbot', 'natural language processing', 'automation']
            }
        elif any(term in name for term in ['design', 'create', 'generate', 'art', 'image']):
            return {
                'capabilities': 'creative content generation and design automation',
                'use_case': 'create professional visual content and design assets',
                'features': ['AI-powered generation', 'Template customization', 'Brand consistency', 'Multiple format export', 'Style transfer'],
                'tags': ['design automation', 'content generation', 'creative AI', 'visual content']
            }
        elif any(term in name for term in ['data', 'analytics', 'insight', 'report', 'dashboard']):
            return {
                'capabilities': 'data processing and analytical insights',
                'use_case': 'analyze complex datasets and generate actionable insights',
                'features': ['Data visualization', 'Automated reporting', 'Trend analysis', 'Custom dashboards', 'Real-time monitoring'],
                'tags': ['data analytics', 'business intelligence', 'reporting', 'visualization']
            }
        else:
            return {
                'capabilities': 'advanced automation and optimization features',
                'use_case': 'streamline workflows and automate repetitive tasks',
                'features': ['Process automation', 'Workflow optimization', 'Task scheduling', 'Integration capabilities', 'Performance monitoring'],
                'tags': ['automation', 'productivity', 'workflow optimization', 'business tools']
            }
    
    def get_company_info(self, tool_name: str, website_url: str) -> Dict[str, Any]:
        """Get company-specific information"""
        prompt = f"""
        Provide company information for "{tool_name}" (website: {website_url}) in JSON format:
        {
            "founded_year": 2023,
            "employee_count_range": "1-10|11-50|51-200|201-500|501-1000|1001-5000|5000+",
            "funding_stage": "PRE_SEED|SEED|SERIES_A|SERIES_B|SERIES_C|SERIES_D_PLUS|PUBLIC|Unknown",
            "location_summary": "City, Country or Remote",
            "social_links": {
                "twitter": "handle_without_@",
                "linkedin": "company/profile-name"
            }
        }
        
        Only include verified information. Use null for unknown fields.
        """
        
        response = self._call_perplexity(prompt, max_tokens=500)
        
        if response:
            try:
                json_match = re.search(r'\{.*\}', response, re.DOTALL)
                if json_match:
                    return json.loads(json_match.group())
            except:
                pass
        
        return {
            "founded_year": None,
            "employee_count_range": "Unknown", 
            "funding_stage": "Unknown",
            "location_summary": "Unknown",
            "social_links": {}
        }