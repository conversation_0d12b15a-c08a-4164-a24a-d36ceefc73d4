"""
Structured Data Extraction System
Extracts rich structured data from websites including JSON-LD, microdata, RDFa,
pricing tables, feature comparisons, and content hierarchy analysis.
"""

import json
import re
import logging
from typing import Dict, List, Any, Optional, Tuple
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup, Tag
import requests
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class StructuredDataType(Enum):
    JSON_LD = "json-ld"
    MICRODATA = "microdata"
    RDFA = "rdfa"
    PRICING_TABLE = "pricing_table"
    FEATURE_COMPARISON = "feature_comparison"
    CONTENT_HIERARCHY = "content_hierarchy"

@dataclass
class StructuredDataResult:
    """Result of structured data extraction"""
    data_type: StructuredDataType
    content: Dict[str, Any]
    confidence: float
    source_location: str

class StructuredDataExtractor:
    """
    Advanced structured data extraction system for comprehensive website analysis
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Common pricing indicators
        self.pricing_indicators = [
            'price', 'cost', 'pricing', 'plan', 'subscription', 'fee', 'rate',
            'monthly', 'yearly', 'annual', 'per month', 'per year', '$', '€', '£'
        ]
        
        # Feature comparison indicators
        self.feature_indicators = [
            'feature', 'capability', 'includes', 'benefit', 'advantage',
            'comparison', 'vs', 'versus', 'compare', 'difference'
        ]
        
        # Content hierarchy selectors
        self.hierarchy_selectors = [
            'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
            '.title', '.heading', '.header',
            '[role="heading"]', '.section-title'
        ]

    def extract_all_structured_data(self, url: str, html_content: str) -> List[StructuredDataResult]:
        """
        Extract all types of structured data from a webpage
        """
        results = []
        soup = BeautifulSoup(html_content, 'html.parser')
        
        try:
            # Extract JSON-LD
            json_ld_results = self._extract_json_ld(soup)
            results.extend(json_ld_results)
            
            # Extract Microdata
            microdata_results = self._extract_microdata(soup)
            results.extend(microdata_results)
            
            # Extract RDFa
            rdfa_results = self._extract_rdfa(soup)
            results.extend(rdfa_results)
            
            # Extract pricing tables
            pricing_results = self._extract_pricing_tables(soup, url)
            results.extend(pricing_results)
            
            # Extract feature comparisons
            feature_results = self._extract_feature_comparisons(soup)
            results.extend(feature_results)
            
            # Extract content hierarchy
            hierarchy_results = self._extract_content_hierarchy(soup)
            results.extend(hierarchy_results)
            
            self.logger.info(f"Extracted {len(results)} structured data elements from {url}")
            
        except Exception as e:
            self.logger.error(f"Error extracting structured data from {url}: {str(e)}")
        
        return results

    def _extract_json_ld(self, soup: BeautifulSoup) -> List[StructuredDataResult]:
        """Extract JSON-LD structured data"""
        results = []
        
        json_ld_scripts = soup.find_all('script', type='application/ld+json')
        
        for i, script in enumerate(json_ld_scripts):
            try:
                if script.string:
                    data = json.loads(script.string.strip())
                    
                    result = StructuredDataResult(
                        data_type=StructuredDataType.JSON_LD,
                        content=data,
                        confidence=0.95,  # JSON-LD is highly reliable
                        source_location=f"script[type='application/ld+json'][{i}]"
                    )
                    results.append(result)
                    
            except json.JSONDecodeError as e:
                self.logger.warning(f"Invalid JSON-LD in script {i}: {str(e)}")
                continue
        
        return results

    def _extract_microdata(self, soup: BeautifulSoup) -> List[StructuredDataResult]:
        """Extract Microdata structured data"""
        results = []
        
        # Find elements with itemscope
        microdata_elements = soup.find_all(attrs={'itemscope': True})
        
        for i, element in enumerate(microdata_elements):
            try:
                microdata = self._parse_microdata_element(element)
                
                if microdata:
                    result = StructuredDataResult(
                        data_type=StructuredDataType.MICRODATA,
                        content=microdata,
                        confidence=0.85,
                        source_location=f"[itemscope][{i}]"
                    )
                    results.append(result)
                    
            except Exception as e:
                self.logger.warning(f"Error parsing microdata element {i}: {str(e)}")
                continue
        
        return results

    def _parse_microdata_element(self, element: Tag) -> Dict[str, Any]:
        """Parse a single microdata element"""
        data = {}
        
        # Get itemtype
        itemtype = element.get('itemtype')
        if itemtype:
            data['@type'] = itemtype
        
        # Find all itemprop elements within this scope
        props = element.find_all(attrs={'itemprop': True})
        
        for prop in props:
            prop_name = prop.get('itemprop')
            prop_value = self._get_microdata_value(prop)
            
            if prop_name and prop_value:
                if prop_name in data:
                    # Handle multiple values
                    if not isinstance(data[prop_name], list):
                        data[prop_name] = [data[prop_name]]
                    data[prop_name].append(prop_value)
                else:
                    data[prop_name] = prop_value
        
        return data

    def _get_microdata_value(self, element: Tag) -> Any:
        """Get the value from a microdata property element"""
        # Check for specific value attributes
        if element.name in ['meta']:
            return element.get('content', '')
        elif element.name in ['time']:
            return element.get('datetime', element.get_text(strip=True))
        elif element.name in ['img', 'audio', 'video', 'source']:
            return element.get('src', '')
        elif element.name in ['a', 'area', 'link']:
            return element.get('href', '')
        elif element.name in ['object']:
            return element.get('data', '')
        else:
            return element.get_text(strip=True)

    def _extract_rdfa(self, soup: BeautifulSoup) -> List[StructuredDataResult]:
        """Extract RDFa structured data"""
        results = []
        
        # Find elements with RDFa attributes
        rdfa_elements = soup.find_all(attrs={'typeof': True})
        
        for i, element in enumerate(rdfa_elements):
            try:
                rdfa_data = self._parse_rdfa_element(element)
                
                if rdfa_data:
                    result = StructuredDataResult(
                        data_type=StructuredDataType.RDFA,
                        content=rdfa_data,
                        confidence=0.80,
                        source_location=f"[typeof][{i}]"
                    )
                    results.append(result)
                    
            except Exception as e:
                self.logger.warning(f"Error parsing RDFa element {i}: {str(e)}")
                continue
        
        return results

    def _parse_rdfa_element(self, element: Tag) -> Dict[str, Any]:
        """Parse a single RDFa element"""
        data = {}
        
        # Get typeof
        typeof = element.get('typeof')
        if typeof:
            data['@type'] = typeof
        
        # Get resource/about
        resource = element.get('resource') or element.get('about')
        if resource:
            data['@id'] = resource
        
        # Find all property elements
        props = element.find_all(attrs={'property': True})
        
        for prop in props:
            prop_name = prop.get('property')
            prop_value = self._get_rdfa_value(prop)
            
            if prop_name and prop_value:
                data[prop_name] = prop_value
        
        return data

    def _get_rdfa_value(self, element: Tag) -> Any:
        """Get the value from an RDFa property element"""
        # Check for content attribute first
        content = element.get('content')
        if content:
            return content
        
        # Check for resource/href
        resource = element.get('resource') or element.get('href')
        if resource:
            return resource
        
        # Fall back to text content
        return element.get_text(strip=True)

    def _extract_pricing_tables(self, soup: BeautifulSoup, base_url: str) -> List[StructuredDataResult]:
        """Extract pricing table information"""
        results = []
        
        # Look for pricing-related elements
        pricing_selectors = [
            '.pricing', '.price', '.plan', '.subscription',
            '[class*="pricing"]', '[class*="price"]', '[class*="plan"]',
            'table[class*="pricing"]', 'div[class*="pricing"]'
        ]
        
        for selector in pricing_selectors:
            elements = soup.select(selector)
            
            for i, element in enumerate(elements):
                try:
                    pricing_data = self._parse_pricing_element(element)
                    
                    if pricing_data and self._is_valid_pricing_data(pricing_data):
                        result = StructuredDataResult(
                            data_type=StructuredDataType.PRICING_TABLE,
                            content=pricing_data,
                            confidence=self._calculate_pricing_confidence(pricing_data),
                            source_location=f"{selector}[{i}]"
                        )
                        results.append(result)
                        
                except Exception as e:
                    self.logger.warning(f"Error parsing pricing element: {str(e)}")
                    continue
        
        return results

    def _parse_pricing_element(self, element: Tag) -> Dict[str, Any]:
        """Parse a pricing table element"""
        pricing_data = {
            'plans': [],
            'features': [],
            'currency': None,
            'billing_periods': []
        }
        
        # Extract text content
        text = element.get_text()
        
        # Look for currency symbols
        currency_matches = re.findall(r'[$€£¥₹]', text)
        if currency_matches:
            pricing_data['currency'] = currency_matches[0]
        
        # Look for prices
        price_patterns = [
            r'[$€£¥₹]\s*(\d+(?:,\d{3})*(?:\.\d{2})?)',  # $99.99
            r'(\d+(?:,\d{3})*(?:\.\d{2})?)\s*[$€£¥₹]',  # 99.99$
            r'(\d+(?:,\d{3})*(?:\.\d{2})?)\s*(?:per|/)\s*(?:month|year|mo|yr)',  # 99 per month
        ]
        
        prices = []
        for pattern in price_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            prices.extend(matches)
        
        # Look for billing periods
        period_patterns = [
            r'(?:per|/)\s*(month|year|mo|yr|monthly|yearly|annual)',
            r'(month|year|mo|yr|monthly|yearly|annual)ly?'
        ]
        
        periods = []
        for pattern in period_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            periods.extend(matches)
        
        pricing_data['prices'] = list(set(prices))
        pricing_data['billing_periods'] = list(set(periods))
        
        # Look for plan names
        plan_elements = element.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6', '.plan-name', '.title'])
        for plan_elem in plan_elements:
            plan_text = plan_elem.get_text(strip=True)
            if plan_text and len(plan_text) < 50:  # Reasonable plan name length
                pricing_data['plans'].append(plan_text)
        
        return pricing_data

    def _is_valid_pricing_data(self, pricing_data: Dict[str, Any]) -> bool:
        """Check if extracted pricing data is valid"""
        return (
            len(pricing_data.get('prices', [])) > 0 or
            len(pricing_data.get('plans', [])) > 0 or
            pricing_data.get('currency') is not None
        )

    def _calculate_pricing_confidence(self, pricing_data: Dict[str, Any]) -> float:
        """Calculate confidence score for pricing data"""
        confidence = 0.0
        
        if pricing_data.get('prices'):
            confidence += 0.4
        if pricing_data.get('currency'):
            confidence += 0.2
        if pricing_data.get('billing_periods'):
            confidence += 0.2
        if pricing_data.get('plans'):
            confidence += 0.2
        
        return min(confidence, 1.0)

    def _extract_feature_comparisons(self, soup: BeautifulSoup) -> List[StructuredDataResult]:
        """Extract feature comparison tables and lists"""
        results = []
        
        # Look for comparison-related elements
        comparison_selectors = [
            'table[class*="comparison"]', 'table[class*="feature"]',
            '.comparison', '.features', '.feature-list',
            '[class*="comparison"]', '[class*="feature"]'
        ]
        
        for selector in comparison_selectors:
            elements = soup.select(selector)
            
            for i, element in enumerate(elements):
                try:
                    feature_data = self._parse_feature_element(element)
                    
                    if feature_data and self._is_valid_feature_data(feature_data):
                        result = StructuredDataResult(
                            data_type=StructuredDataType.FEATURE_COMPARISON,
                            content=feature_data,
                            confidence=self._calculate_feature_confidence(feature_data),
                            source_location=f"{selector}[{i}]"
                        )
                        results.append(result)
                        
                except Exception as e:
                    self.logger.warning(f"Error parsing feature element: {str(e)}")
                    continue
        
        return results

    def _parse_feature_element(self, element: Tag) -> Dict[str, Any]:
        """Parse a feature comparison element"""
        feature_data = {
            'features': [],
            'categories': [],
            'comparisons': []
        }
        
        # Extract features from lists
        list_items = element.find_all(['li', 'tr', 'div'])
        for item in list_items:
            text = item.get_text(strip=True)
            if text and len(text) < 200:  # Reasonable feature length
                # Check if it looks like a feature
                if any(indicator in text.lower() for indicator in self.feature_indicators):
                    feature_data['features'].append(text)
        
        # Extract from table headers
        headers = element.find_all(['th', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'])
        for header in headers:
            text = header.get_text(strip=True)
            if text and len(text) < 100:
                feature_data['categories'].append(text)
        
        return feature_data

    def _is_valid_feature_data(self, feature_data: Dict[str, Any]) -> bool:
        """Check if extracted feature data is valid"""
        return (
            len(feature_data.get('features', [])) > 0 or
            len(feature_data.get('categories', [])) > 0
        )

    def _calculate_feature_confidence(self, feature_data: Dict[str, Any]) -> float:
        """Calculate confidence score for feature data"""
        confidence = 0.0
        
        feature_count = len(feature_data.get('features', []))
        category_count = len(feature_data.get('categories', []))
        
        if feature_count > 0:
            confidence += min(feature_count * 0.1, 0.6)
        if category_count > 0:
            confidence += min(category_count * 0.1, 0.4)
        
        return min(confidence, 1.0)

    def _extract_content_hierarchy(self, soup: BeautifulSoup) -> List[StructuredDataResult]:
        """Extract content hierarchy and structure"""
        results = []
        
        try:
            hierarchy_data = {
                'headings': [],
                'sections': [],
                'navigation': [],
                'structure_score': 0.0
            }
            
            # Extract headings with hierarchy
            for level in range(1, 7):  # h1 to h6
                headings = soup.find_all(f'h{level}')
                for heading in headings:
                    text = heading.get_text(strip=True)
                    if text:
                        hierarchy_data['headings'].append({
                            'level': level,
                            'text': text,
                            'id': heading.get('id'),
                            'classes': heading.get('class', [])
                        })
            
            # Extract sections
            sections = soup.find_all(['section', 'article', 'div[class*="section"]'])
            for section in sections:
                section_data = {
                    'tag': section.name,
                    'id': section.get('id'),
                    'classes': section.get('class', []),
                    'heading_count': len(section.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6']))
                }
                hierarchy_data['sections'].append(section_data)
            
            # Extract navigation
            nav_elements = soup.find_all(['nav', 'ul[class*="nav"]', 'div[class*="nav"]'])
            for nav in nav_elements:
                links = nav.find_all('a')
                nav_data = {
                    'tag': nav.name,
                    'classes': nav.get('class', []),
                    'link_count': len(links),
                    'links': [{'text': link.get_text(strip=True), 'href': link.get('href')} for link in links[:10]]  # Limit to 10
                }
                hierarchy_data['navigation'].append(nav_data)
            
            # Calculate structure score
            hierarchy_data['structure_score'] = self._calculate_structure_score(hierarchy_data)
            
            if hierarchy_data['headings'] or hierarchy_data['sections']:
                result = StructuredDataResult(
                    data_type=StructuredDataType.CONTENT_HIERARCHY,
                    content=hierarchy_data,
                    confidence=0.90,
                    source_location="document_structure"
                )
                results.append(result)
                
        except Exception as e:
            self.logger.warning(f"Error extracting content hierarchy: {str(e)}")
        
        return results

    def _calculate_structure_score(self, hierarchy_data: Dict[str, Any]) -> float:
        """Calculate a score for content structure quality"""
        score = 0.0
        
        # Score based on heading hierarchy
        headings = hierarchy_data.get('headings', [])
        if headings:
            # Check for proper h1 usage
            h1_count = sum(1 for h in headings if h['level'] == 1)
            if h1_count == 1:
                score += 0.3
            elif h1_count == 0:
                score += 0.1
            
            # Check for logical hierarchy
            levels = [h['level'] for h in headings]
            if len(set(levels)) > 1:  # Multiple heading levels
                score += 0.2
            
            # Score based on heading count
            score += min(len(headings) * 0.05, 0.3)
        
        # Score based on sections
        sections = hierarchy_data.get('sections', [])
        if sections:
            score += min(len(sections) * 0.1, 0.2)
        
        return min(score, 1.0)

    def get_structured_data_summary(self, results: List[StructuredDataResult]) -> Dict[str, Any]:
        """Generate a summary of all extracted structured data"""
        summary = {
            'total_elements': len(results),
            'by_type': {},
            'high_confidence_count': 0,
            'average_confidence': 0.0,
            'key_findings': []
        }
        
        if not results:
            return summary
        
        # Group by type
        for result in results:
            data_type = result.data_type.value
            if data_type not in summary['by_type']:
                summary['by_type'][data_type] = 0
            summary['by_type'][data_type] += 1
            
            if result.confidence >= 0.8:
                summary['high_confidence_count'] += 1
        
        # Calculate average confidence
        summary['average_confidence'] = sum(r.confidence for r in results) / len(results)
        
        # Extract key findings
        for result in results:
            if result.confidence >= 0.8:
                if result.data_type == StructuredDataType.JSON_LD:
                    content = result.content
                    if isinstance(content, dict) and '@type' in content:
                        summary['key_findings'].append(f"JSON-LD: {content['@type']}")
                elif result.data_type == StructuredDataType.PRICING_TABLE:
                    prices = result.content.get('prices', [])
                    if prices:
                        summary['key_findings'].append(f"Pricing: {len(prices)} price points found")
                elif result.data_type == StructuredDataType.FEATURE_COMPARISON:
                    features = result.content.get('features', [])
                    if features:
                        summary['key_findings'].append(f"Features: {len(features)} features identified")
        
        return summary
