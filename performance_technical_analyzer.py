"""
Performance and Technical Analysis System
Analyzes website performance metrics, mobile-friendliness, responsive design,
and extracts technical specifications and system requirements.
"""

import re
import time
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
from bs4 import BeautifulSoup, Tag
import requests
from urllib.parse import urljoin, urlparse
import json

logger = logging.getLogger(__name__)

class PerformanceMetric(Enum):
    LOAD_TIME = "load_time"
    PAGE_SIZE = "page_size"
    REQUESTS_COUNT = "requests_count"
    CORE_WEB_VITALS = "core_web_vitals"
    MOBILE_FRIENDLY = "mobile_friendly"
    RESPONSIVE_DESIGN = "responsive_design"
    TECHNICAL_SPECS = "technical_specs"

@dataclass
class PerformanceResult:
    """Result of performance analysis"""
    metric_type: PerformanceMetric
    value: Any
    score: float  # 0-100 score
    recommendations: List[str]
    metadata: Dict[str, Any]

class PerformanceTechnicalAnalyzer:
    """
    Advanced performance and technical analysis system
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Technical specification indicators
        self.tech_spec_indicators = [
            'requirements', 'specifications', 'system requirements',
            'minimum requirements', 'recommended specs', 'compatibility',
            'supported browsers', 'operating system', 'memory', 'storage',
            'processor', 'ram', 'disk space', 'bandwidth'
        ]
        
        # Mobile indicators
        self.mobile_indicators = [
            'mobile', 'responsive', 'tablet', 'phone', 'ios', 'android',
            'mobile-friendly', 'mobile app', 'responsive design'
        ]
        
        # Performance indicators
        self.performance_indicators = [
            'fast', 'speed', 'performance', 'optimized', 'quick',
            'instant', 'real-time', 'milliseconds', 'seconds'
        ]

    def analyze_performance_and_technical(self, url: str, html_content: str) -> List[PerformanceResult]:
        """
        Perform comprehensive performance and technical analysis
        """
        results = []
        
        try:
            # Analyze load time and basic performance
            load_time_result = self._analyze_load_time(url)
            if load_time_result:
                results.append(load_time_result)
            
            # Analyze page size and resource count
            page_metrics = self._analyze_page_metrics(html_content, url)
            results.extend(page_metrics)
            
            # Analyze mobile-friendliness
            mobile_result = self._analyze_mobile_friendliness(html_content)
            if mobile_result:
                results.append(mobile_result)
            
            # Analyze responsive design
            responsive_result = self._analyze_responsive_design(html_content)
            if responsive_result:
                results.append(responsive_result)
            
            # Extract technical specifications
            tech_specs_result = self._extract_technical_specs(html_content)
            if tech_specs_result:
                results.append(tech_specs_result)
            
            # Analyze Core Web Vitals indicators
            cwv_result = self._analyze_core_web_vitals_indicators(html_content)
            if cwv_result:
                results.append(cwv_result)
            
            self.logger.info(f"Completed performance analysis for {url} with {len(results)} metrics")
            
        except Exception as e:
            self.logger.error(f"Error in performance analysis for {url}: {str(e)}")
        
        return results

    def _analyze_load_time(self, url: str) -> Optional[PerformanceResult]:
        """Analyze page load time"""
        try:
            start_time = time.time()
            response = requests.get(url, timeout=30, headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            })
            load_time = time.time() - start_time
            
            # Calculate score (0-100, where <2s = 100, >10s = 0)
            if load_time <= 2.0:
                score = 100
            elif load_time >= 10.0:
                score = 0
            else:
                score = 100 - ((load_time - 2.0) / 8.0) * 100
            
            # Generate recommendations
            recommendations = []
            if load_time > 3.0:
                recommendations.append("Consider optimizing images and reducing file sizes")
            if load_time > 5.0:
                recommendations.append("Implement caching and CDN for better performance")
            if load_time > 8.0:
                recommendations.append("Critical performance issues - immediate optimization needed")
            
            return PerformanceResult(
                metric_type=PerformanceMetric.LOAD_TIME,
                value=round(load_time, 2),
                score=round(score, 1),
                recommendations=recommendations,
                metadata={
                    'status_code': response.status_code,
                    'response_size': len(response.content),
                    'timestamp': time.time()
                }
            )
            
        except Exception as e:
            self.logger.warning(f"Could not measure load time for {url}: {str(e)}")
            return None

    def _analyze_page_metrics(self, html_content: str, base_url: str) -> List[PerformanceResult]:
        """Analyze page size and resource count"""
        results = []
        soup = BeautifulSoup(html_content, 'html.parser')
        
        try:
            # Calculate page size
            page_size = len(html_content.encode('utf-8'))
            
            # Score based on page size (0-100, where <100KB = 100, >1MB = 0)
            if page_size <= 100000:  # 100KB
                size_score = 100
            elif page_size >= 1000000:  # 1MB
                size_score = 0
            else:
                size_score = 100 - ((page_size - 100000) / 900000) * 100
            
            size_recommendations = []
            if page_size > 500000:  # 500KB
                size_recommendations.append("Large page size - consider optimizing HTML and inline content")
            if page_size > 1000000:  # 1MB
                size_recommendations.append("Critical: Page size too large - implement compression and optimization")
            
            results.append(PerformanceResult(
                metric_type=PerformanceMetric.PAGE_SIZE,
                value=page_size,
                score=round(size_score, 1),
                recommendations=size_recommendations,
                metadata={
                    'size_kb': round(page_size / 1024, 2),
                    'size_mb': round(page_size / (1024 * 1024), 2)
                }
            ))
            
            # Count resources
            resources = {
                'images': len(soup.find_all('img')),
                'scripts': len(soup.find_all('script')),
                'stylesheets': len(soup.find_all('link', rel='stylesheet')),
                'external_links': len(soup.find_all('a', href=True))
            }
            
            total_resources = sum(resources.values())
            
            # Score based on resource count (0-100, where <50 = 100, >200 = 0)
            if total_resources <= 50:
                resource_score = 100
            elif total_resources >= 200:
                resource_score = 0
            else:
                resource_score = 100 - ((total_resources - 50) / 150) * 100
            
            resource_recommendations = []
            if resources['images'] > 20:
                resource_recommendations.append("High image count - consider lazy loading and optimization")
            if resources['scripts'] > 15:
                resource_recommendations.append("Many scripts detected - consider bundling and minification")
            if total_resources > 100:
                resource_recommendations.append("High resource count may impact performance")
            
            results.append(PerformanceResult(
                metric_type=PerformanceMetric.REQUESTS_COUNT,
                value=total_resources,
                score=round(resource_score, 1),
                recommendations=resource_recommendations,
                metadata=resources
            ))
            
        except Exception as e:
            self.logger.warning(f"Error analyzing page metrics: {str(e)}")
        
        return results

    def _analyze_mobile_friendliness(self, html_content: str) -> Optional[PerformanceResult]:
        """Analyze mobile-friendliness indicators"""
        soup = BeautifulSoup(html_content, 'html.parser')
        
        mobile_indicators = {
            'viewport_meta': False,
            'responsive_meta': False,
            'mobile_css': False,
            'touch_friendly': False,
            'mobile_app_links': False
        }
        
        score = 0
        recommendations = []
        
        try:
            # Check for viewport meta tag
            viewport_meta = soup.find('meta', attrs={'name': 'viewport'})
            if viewport_meta:
                mobile_indicators['viewport_meta'] = True
                score += 30
                content = viewport_meta.get('content', '')
                if 'width=device-width' in content:
                    score += 10
            else:
                recommendations.append("Add viewport meta tag for mobile optimization")
            
            # Check for responsive design indicators
            responsive_keywords = ['responsive', 'mobile', 'tablet', 'device-width']
            css_links = soup.find_all('link', rel='stylesheet')
            style_tags = soup.find_all('style')
            
            for element in css_links + style_tags:
                text = str(element).lower()
                if any(keyword in text for keyword in responsive_keywords):
                    mobile_indicators['responsive_meta'] = True
                    score += 20
                    break
            
            # Check for mobile-specific CSS
            if '@media' in html_content.lower():
                mobile_indicators['mobile_css'] = True
                score += 20
            else:
                recommendations.append("Add responsive CSS media queries for better mobile experience")
            
            # Check for touch-friendly elements
            touch_keywords = ['touch', 'tap', 'swipe', 'gesture']
            if any(keyword in html_content.lower() for keyword in touch_keywords):
                mobile_indicators['touch_friendly'] = True
                score += 10
            
            # Check for mobile app links
            app_links = soup.find_all('a', href=re.compile(r'(itunes\.apple\.com|play\.google\.com)'))
            if app_links:
                mobile_indicators['mobile_app_links'] = True
                score += 10
            
            # Generate recommendations based on score
            if score < 50:
                recommendations.append("Poor mobile optimization - implement responsive design")
            elif score < 80:
                recommendations.append("Good mobile support - consider additional optimizations")
            
            return PerformanceResult(
                metric_type=PerformanceMetric.MOBILE_FRIENDLY,
                value=mobile_indicators,
                score=min(score, 100),
                recommendations=recommendations,
                metadata={
                    'has_viewport': mobile_indicators['viewport_meta'],
                    'has_responsive_css': mobile_indicators['mobile_css'],
                    'mobile_app_available': mobile_indicators['mobile_app_links']
                }
            )
            
        except Exception as e:
            self.logger.warning(f"Error analyzing mobile-friendliness: {str(e)}")
            return None

    def _analyze_responsive_design(self, html_content: str) -> Optional[PerformanceResult]:
        """Analyze responsive design implementation"""
        soup = BeautifulSoup(html_content, 'html.parser')
        
        responsive_indicators = {
            'media_queries': 0,
            'flexible_grid': False,
            'flexible_images': False,
            'breakpoints': [],
            'css_frameworks': []
        }
        
        score = 0
        recommendations = []
        
        try:
            # Count media queries
            media_query_pattern = r'@media[^{]*\{[^}]*\}'
            media_queries = re.findall(media_query_pattern, html_content, re.IGNORECASE | re.DOTALL)
            responsive_indicators['media_queries'] = len(media_queries)
            
            if len(media_queries) > 0:
                score += 30
            else:
                recommendations.append("Add CSS media queries for responsive design")
            
            # Extract breakpoints
            breakpoint_pattern = r'@media[^{]*\((?:min-width|max-width):\s*(\d+)px\)'
            breakpoints = re.findall(breakpoint_pattern, html_content, re.IGNORECASE)
            responsive_indicators['breakpoints'] = list(set(breakpoints))
            
            if len(breakpoints) >= 2:
                score += 20
            
            # Check for flexible grid systems
            grid_keywords = ['grid', 'flex', 'column', 'row', 'container']
            css_content = html_content.lower()
            
            grid_count = sum(css_content.count(keyword) for keyword in grid_keywords)
            if grid_count > 5:
                responsive_indicators['flexible_grid'] = True
                score += 25
            
            # Check for responsive images
            img_tags = soup.find_all('img')
            responsive_img_count = 0
            
            for img in img_tags:
                if (img.get('srcset') or 
                    'responsive' in str(img.get('class', [])).lower() or
                    'max-width' in str(img.get('style', '')).lower()):
                    responsive_img_count += 1
            
            if responsive_img_count > 0:
                responsive_indicators['flexible_images'] = True
                score += 15
            else:
                recommendations.append("Implement responsive images with srcset or CSS")
            
            # Check for CSS frameworks
            framework_indicators = {
                'bootstrap': ['bootstrap', 'bs-'],
                'foundation': ['foundation'],
                'bulma': ['bulma'],
                'tailwind': ['tailwind', 'tw-']
            }
            
            for framework, keywords in framework_indicators.items():
                if any(keyword in html_content.lower() for keyword in keywords):
                    responsive_indicators['css_frameworks'].append(framework)
                    score += 10
            
            # Generate recommendations
            if score < 40:
                recommendations.append("Implement comprehensive responsive design strategy")
            elif score < 70:
                recommendations.append("Good responsive foundation - enhance with more breakpoints")
            
            return PerformanceResult(
                metric_type=PerformanceMetric.RESPONSIVE_DESIGN,
                value=responsive_indicators,
                score=min(score, 100),
                recommendations=recommendations,
                metadata={
                    'media_query_count': responsive_indicators['media_queries'],
                    'breakpoint_count': len(responsive_indicators['breakpoints']),
                    'frameworks_detected': responsive_indicators['css_frameworks']
                }
            )
            
        except Exception as e:
            self.logger.warning(f"Error analyzing responsive design: {str(e)}")
            return None

    def _extract_technical_specs(self, html_content: str) -> Optional[PerformanceResult]:
        """Extract technical specifications and system requirements"""
        soup = BeautifulSoup(html_content, 'html.parser')
        
        tech_specs = {
            'system_requirements': [],
            'supported_browsers': [],
            'supported_platforms': [],
            'api_requirements': [],
            'integrations': [],
            'technical_details': []
        }
        
        score = 0
        recommendations = []
        
        try:
            # Look for technical specification sections
            spec_sections = soup.find_all(['div', 'section', 'article'], 
                                        class_=re.compile(r'(requirement|spec|technical|system)', re.I))
            
            # Also search in text content
            text_content = soup.get_text().lower()
            
            # Extract system requirements
            requirement_patterns = [
                r'(windows|mac|linux|ios|android)\s+(\d+(?:\.\d+)?)',
                r'(\d+)\s*gb\s+(ram|memory|storage)',
                r'(chrome|firefox|safari|edge)\s+(\d+)',
                r'(node\.js|python|java|php)\s+(\d+(?:\.\d+)?)'
            ]
            
            for pattern in requirement_patterns:
                matches = re.findall(pattern, text_content, re.IGNORECASE)
                for match in matches:
                    tech_specs['system_requirements'].append(' '.join(match))
            
            if tech_specs['system_requirements']:
                score += 30
            
            # Extract browser support
            browser_keywords = ['chrome', 'firefox', 'safari', 'edge', 'internet explorer', 'opera']
            for keyword in browser_keywords:
                if keyword in text_content:
                    tech_specs['supported_browsers'].append(keyword)
            
            if tech_specs['supported_browsers']:
                score += 20
            
            # Extract platform support
            platform_keywords = ['windows', 'mac', 'linux', 'ios', 'android', 'web', 'mobile', 'desktop']
            for keyword in platform_keywords:
                if keyword in text_content:
                    tech_specs['supported_platforms'].append(keyword)
            
            if tech_specs['supported_platforms']:
                score += 20
            
            # Extract API and integration information
            api_keywords = ['api', 'rest', 'graphql', 'webhook', 'integration', 'sdk']
            for keyword in api_keywords:
                if keyword in text_content:
                    tech_specs['api_requirements'].append(keyword)
            
            if tech_specs['api_requirements']:
                score += 15
            
            # Extract integration mentions
            integration_patterns = [
                r'integrat(?:es?|ion)\s+with\s+([a-zA-Z\s]+)',
                r'connect(?:s?|ion)\s+to\s+([a-zA-Z\s]+)',
                r'works\s+with\s+([a-zA-Z\s]+)'
            ]
            
            for pattern in integration_patterns:
                matches = re.findall(pattern, text_content, re.IGNORECASE)
                tech_specs['integrations'].extend([match.strip() for match in matches])
            
            if tech_specs['integrations']:
                score += 15
            
            # Generate recommendations
            if score < 30:
                recommendations.append("Add technical specifications and system requirements")
            if not tech_specs['supported_browsers']:
                recommendations.append("Specify supported browsers for better user guidance")
            if not tech_specs['system_requirements']:
                recommendations.append("Include minimum system requirements")
            
            return PerformanceResult(
                metric_type=PerformanceMetric.TECHNICAL_SPECS,
                value=tech_specs,
                score=min(score, 100),
                recommendations=recommendations,
                metadata={
                    'requirements_found': len(tech_specs['system_requirements']),
                    'browsers_supported': len(tech_specs['supported_browsers']),
                    'platforms_supported': len(tech_specs['supported_platforms']),
                    'integrations_mentioned': len(tech_specs['integrations'])
                }
            )
            
        except Exception as e:
            self.logger.warning(f"Error extracting technical specs: {str(e)}")
            return None

    def _analyze_core_web_vitals_indicators(self, html_content: str) -> Optional[PerformanceResult]:
        """Analyze indicators related to Core Web Vitals"""
        soup = BeautifulSoup(html_content, 'html.parser')
        
        cwv_indicators = {
            'lazy_loading': False,
            'image_optimization': False,
            'critical_css': False,
            'preload_resources': False,
            'compression_hints': False
        }
        
        score = 0
        recommendations = []
        
        try:
            # Check for lazy loading
            lazy_images = soup.find_all('img', loading='lazy')
            if lazy_images:
                cwv_indicators['lazy_loading'] = True
                score += 20
            else:
                recommendations.append("Implement lazy loading for images to improve LCP")
            
            # Check for image optimization indicators
            img_tags = soup.find_all('img')
            optimized_images = 0
            
            for img in img_tags:
                if (img.get('srcset') or 
                    img.get('sizes') or
                    any(fmt in str(img.get('src', '')).lower() for fmt in ['webp', 'avif'])):
                    optimized_images += 1
            
            if optimized_images > 0:
                cwv_indicators['image_optimization'] = True
                score += 20
            else:
                recommendations.append("Optimize images with modern formats and responsive sizing")
            
            # Check for critical CSS indicators
            critical_css_indicators = soup.find_all('style')
            if critical_css_indicators:
                cwv_indicators['critical_css'] = True
                score += 15
            
            # Check for resource preloading
            preload_links = soup.find_all('link', rel='preload')
            if preload_links:
                cwv_indicators['preload_resources'] = True
                score += 15
            else:
                recommendations.append("Consider preloading critical resources")
            
            # Check for compression hints
            if 'gzip' in html_content.lower() or 'brotli' in html_content.lower():
                cwv_indicators['compression_hints'] = True
                score += 10
            
            # Generate overall recommendations
            if score < 40:
                recommendations.append("Implement Core Web Vitals optimizations for better user experience")
            elif score < 70:
                recommendations.append("Good performance foundation - fine-tune for optimal Core Web Vitals")
            
            return PerformanceResult(
                metric_type=PerformanceMetric.CORE_WEB_VITALS,
                value=cwv_indicators,
                score=min(score, 100),
                recommendations=recommendations,
                metadata={
                    'lazy_images_count': len(soup.find_all('img', loading='lazy')),
                    'preload_resources_count': len(soup.find_all('link', rel='preload')),
                    'total_images': len(soup.find_all('img'))
                }
            )
            
        except Exception as e:
            self.logger.warning(f"Error analyzing Core Web Vitals indicators: {str(e)}")
            return None

    def get_performance_summary(self, results: List[PerformanceResult]) -> Dict[str, Any]:
        """Generate summary of performance analysis"""
        summary = {
            'overall_score': 0.0,
            'total_metrics': len(results),
            'by_metric': {},
            'top_recommendations': [],
            'performance_grade': 'F'
        }
        
        if not results:
            return summary
        
        # Calculate overall score
        total_score = sum(result.score for result in results)
        summary['overall_score'] = round(total_score / len(results), 1)
        
        # Determine performance grade
        if summary['overall_score'] >= 90:
            summary['performance_grade'] = 'A'
        elif summary['overall_score'] >= 80:
            summary['performance_grade'] = 'B'
        elif summary['overall_score'] >= 70:
            summary['performance_grade'] = 'C'
        elif summary['overall_score'] >= 60:
            summary['performance_grade'] = 'D'
        else:
            summary['performance_grade'] = 'F'
        
        # Group by metric type
        for result in results:
            metric_type = result.metric_type.value
            summary['by_metric'][metric_type] = {
                'score': result.score,
                'value': result.value,
                'recommendations': result.recommendations
            }
        
        # Collect top recommendations
        all_recommendations = []
        for result in results:
            all_recommendations.extend(result.recommendations)
        
        # Get unique recommendations, prioritizing by frequency
        recommendation_counts = {}
        for rec in all_recommendations:
            recommendation_counts[rec] = recommendation_counts.get(rec, 0) + 1
        
        summary['top_recommendations'] = sorted(
            recommendation_counts.keys(),
            key=lambda x: recommendation_counts[x],
            reverse=True
        )[:5]
        
        return summary
